# AI Categorization System Improvements

## Overview
The Pebl app's AI categorization system has been significantly enhanced to provide more intelligent, context-aware message organization. The improvements focus on better understanding content, smarter subfolder creation, and more accurate categorization decisions.

## Key Improvements

### 1. Enhanced Categorization Prompts
**Before:** Simple prompt asking to categorize into available categories
**After:** Comprehensive prompt with specific rules and context awareness

#### New Prompt Features:
- **Preference for existing categories** - Strongly favors using existing categories over creating new ones
- **Content-specific guidance** - Special handling for movies, shopping, URLs, tasks, and reading materials
- **Hierarchical thinking** - Considers when subfolders would be more appropriate
- **Clear formatting rules** - Ensures consistent category naming

### 2. URL Content Analysis
**New Feature:** Intelligent URL domain analysis for better categorization

#### Supported URL Categories:
- **Shopping:** Amazon, eBay, Etsy, Walmart, Target, BestBuy
- **Entertainment:** Netflix, YouTube, IMDB, Spotify, Hulu, Disney+
- **News:** CNN, BBC, NYTimes, WSJ, Guardian, NPR
- **Learning:** Documentation, Stack Overflow, GitHub, Coursera, Udemy
- **Work:** Slack, Teams, Zoom, Office, Google Drive
- **Health:** Medical sites, fitness, nutrition resources
- **Travel:** Booking, Airbnb, Expedia, travel sites
- **Social:** Twitter, Facebook, Instagram, LinkedIn, Reddit

### 3. Smart Subfolder Creation
**New Feature:** Automatic subfolder suggestions based on content patterns and category size

#### Subfolder Rules:
- **Minimum threshold:** 5+ messages in category before suggesting subfolders
- **Two-tier approach:** Predefined patterns + AI-powered dynamic analysis
- **Content-based logic:** Analyzes message content for natural groupings

#### Predefined Category Subfolders:

**Movies to Watch:**
- Comedy
- Action & Adventure
- Drama & Romance
- Horror & Thriller
- Documentaries

**Shopping:**
- Clothing & Fashion
- Electronics
- Books
- Food & Grocery
- Home & Garden

**To-Do:**
- Urgent (for high-priority tasks)
- Work (office/professional tasks)
- Personal (home/family tasks)

**To-Read:**
- Articles & Blogs
- Books
- Research & Papers

#### Dynamic Subfolder Creation:
**NEW:** AI analyzes ANY category to suggest logical subfolders based on content patterns

**Examples for Life Projects:**
- **Book Writing:** Research, Character Development, Plot Outline, Editing, Publishing
- **House Buying:** Research, Viewing, Financing, Legal, Moving
- **Baby Planning:** Preparation, Medical, Shopping, Nursery Setup
- **Wedding Planning:** Venue, Catering, Photography, Guest Management
- **Career Change:** Skill Building, Networking, Job Search, Interview Prep
- **Business Startup:** Planning, Legal, Marketing, Development, Funding

### 4. Context-Aware Categorization
**New Feature:** Considers existing category structure and patterns

#### How It Works:
1. Analyzes message content and any URLs
2. Checks existing categories for best fit
3. Determines if subfolder creation is appropriate
4. Returns comprehensive categorization decision

## Implementation Details

### New Methods in AIModel.swift

```swift
// Extract URLs from messages for analysis
func extractURLsFromMessage(_ message: String) -> [String]

// Analyze URL domains for categorization hints
func analyzeURLContent(_ url: String) -> String

// Determine if subfolder should be created (predefined + dynamic)
func shouldCreateSubfolder(for message: String, in category: Category) -> (shouldCreate: Bool, subfolderName: String?)

// Check predefined subfolder patterns
private func checkPredefinedSubfolderPatterns(message: String, category: Category) -> (shouldCreate: Bool, subfolderName: String?)?

// AI-powered dynamic subfolder suggestion for ANY category
private func suggestDynamicSubfolder(for message: String, in category: Category) -> (shouldCreate: Bool, subfolderName: String?)

// Analyze existing category content and suggest optimization
func analyzeCategoryForOptimization(_ category: Category) -> [String]

// Main categorization with full context
func categorizeMessageWithContext(_ message: String, categoryManager: CategoryManager) -> (category: Category?, shouldCreateSubfolder: Bool, subfolderName: String?)
```

### Updated CategoryManager Logic
The `categorizeAndAddMessage` method now uses the enhanced context-aware categorization and automatically creates subfolders when appropriate.

## Usage Examples

### Movie Categorization
**Input:** "Watch The Dark Knight - Batman movie"
**Result:** 
- Category: "Movies to Watch"
- Subfolder: "Action & Adventure" (if category has 5+ messages)

### Shopping with URL
**Input:** "https://www.amazon.com/dp/B08N5WRWNW - AirPods Pro"
**Result:**
- URL Analysis: "shopping"
- Category: "Shopping"
- Subfolder: "Electronics" (if category has 5+ messages)

### Urgent Task
**Input:** "URGENT: Submit quarterly report by Friday"
**Result:**
- Category: "To-Do"
- Subfolder: "Urgent" (if category has 5+ messages)

### Creative Life Projects
**Input:** "Research character development techniques for my novel"
**Result:**
- Category: "Book Writing" (new category created)
- Future messages analyzed for subfolders like "Research", "Character Development", "Plot Outline"

**Input:** "Schedule house viewing for Saturday"
**Result:**
- Category: "House Buying" (new category created)
- Future messages organized into "Research", "Viewing", "Financing", "Legal"

**Input:** "Buy prenatal vitamins"
**Result:**
- Category: "Baby Planning" (new category created)
- Future messages organized into "Preparation", "Medical", "Shopping", "Nursery Setup"

## Benefits

### For Users:
1. **Better Organization:** Messages are more accurately categorized
2. **Automatic Subfolders:** Natural groupings emerge automatically
3. **Fewer New Categories:** Reduces category proliferation
4. **URL Intelligence:** Links are categorized based on their actual content

### For Developers:
1. **Extensible System:** Easy to add new URL patterns and subfolder rules
2. **Context Awareness:** System learns from existing structure
3. **Configurable Thresholds:** Subfolder creation rules can be adjusted
4. **Comprehensive Testing:** Test suite validates improvements

## Future Enhancements

### Potential Additions:
1. **Machine Learning:** Learn from user corrections and preferences
2. **Custom Rules:** Allow users to define their own categorization rules
3. **Batch Processing:** Improve categorization of multiple messages
4. **Integration APIs:** Connect with external services for richer content analysis
5. **User Feedback Loop:** Learn from manual recategorizations

## Testing

Run the test suite with:
```swift
AIModelTests.runTests()
```

This will demonstrate the enhanced categorization with various message types and show the improvements in action.
