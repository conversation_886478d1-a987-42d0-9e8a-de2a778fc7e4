//
//  CategorizationConfiguration.swift
//  Pebl
//
//  Created by AI Assistant on 6/17/25.
//

import Foundation

// MARK: - Configuration Models

struct CategorizationConfiguration: Codable {
    let subfolderPatterns: [String: SubfolderPatternConfig]
    let urlCategories: [String]
    let defaultCategories: [DefaultCategoryConfig]
    let aiSettings: AISettings
    let caching: CachingConfig
}

struct SubfolderPatternConfig: Codable {
    let triggers: [String]
    let patterns: [PatternRule]
}

struct PatternRule: Codable {
    let keywords: [String]
    let subfolder: String
}

struct DefaultCategoryConfig: Codable {
    let name: String
    let sfSymbol: String
}

struct AISettings: Codable {
    let model: String
    let maxTokens: MaxTokensConfig
    let timeouts: TimeoutsConfig
    let retryAttempts: Int
}

struct MaxTokensConfig: Codable {
    let categorization: Int
    let subfolderSuggestion: Int
    let urlAnalysis: Int
    let sfSymbol: Int
}

struct TimeoutsConfig: Codable {
    let urlFetch: Double
    let aiRequest: Double
}

struct CachingConfig: Codable {
    let enabled: Bool
    let urlAnalysisTTL: TimeInterval
    let categoryAnalysisTTL: TimeInterval
    let maxCacheSize: Int
}

// MARK: - Configuration Manager

class ConfigurationManager {
    static let shared = ConfigurationManager()
    
    private var configuration: CategorizationConfiguration?
    private let configFileName = "CategorizationConfig"
    
    private init() {
        loadConfiguration()
    }
    
    func getConfiguration() -> CategorizationConfiguration {
        guard let config = configuration else {
            fatalError("Configuration not loaded. Ensure CategorizationConfig.json exists in the bundle.")
        }
        return config
    }
    
    private func loadConfiguration() {
        guard let url = Bundle.main.url(forResource: configFileName, withExtension: "json"),
              let data = try? Data(contentsOf: url) else {
            fatalError("Could not find \(configFileName).json in bundle")
        }
        
        do {
            configuration = try JSONDecoder().decode(CategorizationConfiguration.self, from: data)
        } catch {
            fatalError("Could not decode configuration: \(error)")
        }
    }
    
    // MARK: - Convenience Methods
    
    func getSubfolderPatterns() -> [String: SubfolderPatternConfig] {
        return getConfiguration().subfolderPatterns
    }
    
    func getURLCategories() -> [String] {
        return getConfiguration().urlCategories
    }
    
    func getDefaultCategories() -> [DefaultCategoryConfig] {
        return getConfiguration().defaultCategories
    }
    
    func getAISettings() -> AISettings {
        return getConfiguration().aiSettings
    }
    
    func getCachingConfig() -> CachingConfig {
        return getConfiguration().caching
    }
}

// MARK: - Configuration Validation

extension ConfigurationManager {
    func validateConfiguration() -> [String] {
        var errors: [String] = []
        let config = getConfiguration()
        
        // Validate AI settings
        if config.aiSettings.model.isEmpty {
            errors.append("AI model cannot be empty")
        }
        
        if config.aiSettings.retryAttempts < 0 {
            errors.append("Retry attempts cannot be negative")
        }
        
        // Validate timeouts
        if config.aiSettings.timeouts.urlFetch <= 0 {
            errors.append("URL fetch timeout must be positive")
        }
        
        if config.aiSettings.timeouts.aiRequest <= 0 {
            errors.append("AI request timeout must be positive")
        }
        
        // Validate caching
        if config.caching.maxCacheSize <= 0 {
            errors.append("Max cache size must be positive")
        }
        
        // Validate default categories
        for category in config.defaultCategories {
            if category.name.isEmpty {
                errors.append("Default category name cannot be empty")
            }
            if category.sfSymbol.isEmpty {
                errors.append("Default category SF symbol cannot be empty")
            }
        }
        
        return errors
    }
}
