{"subfolderPatterns": {"movies": {"triggers": ["movie", "film", "watch"], "patterns": [{"keywords": ["comedy", "funny", "laugh"], "subfolder": "Comedy"}, {"keywords": ["action", "adventure", "thriller"], "subfolder": "Action & Adventure"}, {"keywords": ["drama", "romantic", "romance"], "subfolder": "Drama & Romance"}, {"keywords": ["horror", "scary"], "subfolder": "Horror & Thriller"}, {"keywords": ["documentary", "docu"], "subfolder": "Documentaries"}]}, "shopping": {"triggers": ["shop"], "patterns": [{"keywords": ["cloth", "shirt", "dress", "shoe", "fashion"], "subfolder": "Clothing & Fashion"}, {"keywords": ["electronic", "phone", "computer", "gadget", "tech"], "subfolder": "Electronics"}, {"keywords": ["book", "read"], "subfolder": "Books"}, {"keywords": ["food", "grocery", "kitchen"], "subfolder": "Food & Grocery"}, {"keywords": ["home", "furniture", "decor"], "subfolder": "Home & Garden"}]}, "tasks": {"triggers": ["to-do", "todo", "task"], "patterns": [{"keywords": ["urgent", "asap", "important"], "subfolder": "<PERSON><PERSON>"}, {"keywords": ["work", "office", "meeting"], "subfolder": "Work"}, {"keywords": ["personal", "home", "family"], "subfolder": "Personal"}]}, "reading": {"triggers": ["read"], "patterns": [{"keywords": ["article", "blog", "news"], "subfolder": "Articles & Blogs"}, {"keywords": ["book", "novel"], "subfolder": "Books"}, {"keywords": ["research", "paper", "study"], "subfolder": "Research & Papers"}]}}, "urlCategories": ["shopping", "entertainment", "news", "learning", "work", "health", "travel", "finance", "food", "technology", "lifestyle", "web_link"], "defaultCategories": [{"name": "To-Read", "sfSymbol": "book.circle"}, {"name": "Shopping", "sfSymbol": "cart"}, {"name": "To-Do", "sfSymbol": "checkmark.square"}, {"name": "Movies to Watch", "sfSymbol": "film"}, {"name": "Appointments", "sfSymbol": "calendar"}], "aiSettings": {"model": "gpt-4", "maxTokens": {"categorization": 30, "subfolderSuggestion": 50, "urlAnalysis": 20, "sfSymbol": 20}, "timeouts": {"urlFetch": 10.0, "aiRequest": 30.0}, "retryAttempts": 2}, "caching": {"enabled": true, "urlAnalysisTTL": 86400, "categoryAnalysisTTL": 3600, "maxCacheSize": 1000}}