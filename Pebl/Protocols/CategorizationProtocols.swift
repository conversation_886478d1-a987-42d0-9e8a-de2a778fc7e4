//
//  CategorizationProtocols.swift
//  Pebl
//
//  Created by AI Assistant on 6/17/25.
//

import Foundation

// MARK: - Core Protocols

/// Protocol for analyzing URLs and determining their content category
protocol URLAnalyzer {
    func analyzeURL(_ url: String) async throws -> URLAnalysisResult
    func analyzeURLWithContent(_ url: String) async throws -> URLAnalysisResult
}

/// Protocol for categorizing messages using AI
protocol MessageCategorizer {
    func categorizeMessage(_ message: String, availableCategories: [String]) async throws -> String
    func categorizeMessageWithContext(_ message: String, categoryManager: CategoryManagerProtocol) async throws -> CategorizationResult
}

/// Protocol for suggesting subfolders
protocol SubfolderAnalyzer {
    func shouldCreateSubfolder(for message: String, in category: Category) async throws -> SubfolderSuggestion
    func analyzeExistingCategory(_ category: Category) async throws -> [String]
}

/// Protocol for generating SF Symbols
protocol SymbolGenerator {
    func getSFSymbol(for categoryName: String) async throws -> String
}

/// Protocol for caching analysis results
protocol AnalysisCache {
    func getCachedResult<T: Codable>(for key: String, type: T.Type) -> T?
    func setCachedResult<T: Codable>(_ result: T, for key: String, ttl: TimeInterval)
    func clearCache()
    func clearExpiredEntries()
}

/// Protocol for category management
protocol CategoryManagerProtocol: AnyObject {
    var rootCategories: [Category] { get set }
    func findCategory(named name: String) -> Category?
    func getAllCategoryNames() -> [String]
    func addRootCategory(name: String, sfSymbol: String) -> Category
    func removeRootCategory(_ category: Category)
}

// MARK: - Result Types

struct URLAnalysisResult: Codable {
    let category: String
    let confidence: Double
    let metadata: URLMetadata?
    
    struct URLMetadata: Codable {
        let title: String?
        let description: String?
        let keywords: String?
        let domain: String
    }
}

struct CategorizationResult {
    let category: Category?
    let shouldCreateSubfolder: Bool
    let subfolderName: String?
    let confidence: Double
}

struct SubfolderSuggestion {
    let shouldCreate: Bool
    let subfolderName: String?
    let confidence: Double
    let reasoning: String?
}

// MARK: - Error Types

enum CategorizationError: Error, LocalizedError {
    case configurationMissing
    case invalidConfiguration(String)
    case networkError(Error)
    case apiKeyMissing
    case invalidResponse
    case timeout
    case rateLimitExceeded
    case invalidURL(String)
    case cacheError(String)
    
    var errorDescription: String? {
        switch self {
        case .configurationMissing:
            return "Configuration file is missing"
        case .invalidConfiguration(let details):
            return "Invalid configuration: \(details)"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        case .apiKeyMissing:
            return "API key is missing from environment variables"
        case .invalidResponse:
            return "Invalid response from AI service"
        case .timeout:
            return "Request timed out"
        case .rateLimitExceeded:
            return "API rate limit exceeded"
        case .invalidURL(let url):
            return "Invalid URL: \(url)"
        case .cacheError(let details):
            return "Cache error: \(details)"
        }
    }
}

// MARK: - AI Service Protocol

protocol AIServiceProtocol {
    func makeRequest(prompt: String, maxTokens: Int, timeout: TimeInterval) async throws -> String
}

// MARK: - Pattern Matching Protocol

protocol PatternMatcher {
    func findMatchingPattern(for message: String, in category: Category) -> SubfolderSuggestion?
}

// MARK: - Analytics Protocol

protocol CategorizationAnalytics {
    func trackCategorization(message: String, category: String, confidence: Double)
    func trackSubfolderCreation(category: String, subfolder: String)
    func trackURLAnalysis(url: String, category: String, method: String)
    func trackError(_ error: CategorizationError)
}

// MARK: - Configuration Protocol

protocol ConfigurationProvider {
    func getSubfolderPatterns() -> [String: SubfolderPatternConfig]
    func getURLCategories() -> [String]
    func getDefaultCategories() -> [DefaultCategoryConfig]
    func getAISettings() -> AISettings
    func getCachingConfig() -> CachingConfig
}

// MARK: - Validation Protocol

protocol Validator {
    associatedtype T
    func validate(_ item: T) -> ValidationResult
}

struct ValidationResult {
    let isValid: Bool
    let errors: [String]
    let warnings: [String]
    
    static let valid = ValidationResult(isValid: true, errors: [], warnings: [])
    
    static func invalid(errors: [String], warnings: [String] = []) -> ValidationResult {
        return ValidationResult(isValid: false, errors: errors, warnings: warnings)
    }
}

// MARK: - Factory Protocol

protocol CategorizationServiceFactory {
    func createURLAnalyzer() -> URLAnalyzer
    func createMessageCategorizer() -> MessageCategorizer
    func createSubfolderAnalyzer() -> SubfolderAnalyzer
    func createSymbolGenerator() -> SymbolGenerator
    func createCache() -> AnalysisCache
    func createAIService() -> AIServiceProtocol
}
