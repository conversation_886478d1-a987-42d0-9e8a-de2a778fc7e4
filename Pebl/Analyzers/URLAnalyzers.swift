//
//  URLAnalyzers.swift
//  Pebl
//
//  Created by AI Assistant on 6/17/25.
//

import Foundation

// MARK: - AI-Powered URL Analyzer

class AIURLAnalyzer: URLAnalyzer {
    private let aiService: AIServiceProtocol
    private let cache: AnalysisCache
    private let config: ConfigurationProvider
    private let logger: Logger
    
    init(aiService: AIServiceProtocol, 
         cache: AnalysisCache, 
         config: ConfigurationProvider,
         logger: Logger = ConsoleLogger()) {
        self.aiService = aiService
        self.cache = cache
        self.config = config
        self.logger = logger
    }
    
    func analyzeURL(_ url: String) async throws -> URLAnalysisResult {
        // Validate URL
        let validationResult = AnyValidator<String>.urlValidator.validate(url)
        guard validationResult.isValid else {
            throw CategorizationError.invalidURL(validationResult.errors.joined(separator: ", "))
        }
        
        // Check cache first
        let cacheKey = CacheKeyGenerator.urlAnalysisKey(for: url)
        if let cachedResult = cache.getCachedResult(for: cacheKey, type: URLAnalysisResult.self) {
            logger.debug("Using cached URL analysis for: \(url)")
            return cachedResult
        }
        
        // Perform AI analysis
        let result = try await performAIAnalysis(url: url)
        
        // Cache the result
        let cachingConfig = config.getCachingConfig()
        if cachingConfig.enabled {
            cache.setCachedResult(result, for: cacheKey, ttl: cachingConfig.urlAnalysisTTL)
        }
        
        return result
    }
    
    func analyzeURLWithContent(_ url: String) async throws -> URLAnalysisResult {
        // First try to fetch content
        do {
            let metadata = try await fetchURLMetadata(url)
            return try await performContentAnalysis(url: url, metadata: metadata)
        } catch {
            logger.warning("Failed to fetch URL content, falling back to domain analysis: \(error)")
            return try await analyzeURL(url)
        }
    }
    
    private func performAIAnalysis(url: String) async throws -> URLAnalysisResult {
        let urlCategories = config.getURLCategories()
        let aiSettings = config.getAISettings()
        
        let prompt = """
        Analyze this URL and determine what category of content it likely represents: \(url)
        
        Based on the URL structure, domain name, and path, categorize this as one of:
        \(urlCategories.joined(separator: ", "))
        
        Consider:
        - Domain name patterns and keywords
        - URL path structure
        - Common website purposes
        - Industry-standard domain conventions
        
        Return only the category name.
        """
        
        let category = try await aiService.makeRequest(
            prompt: prompt,
            maxTokens: aiSettings.maxTokens.urlAnalysis,
            timeout: aiSettings.timeouts.aiRequest
        )
        
        let domain = URL(string: url)?.host ?? url
        
        return URLAnalysisResult(
            category: category.trimmingCharacters(in: .whitespacesAndNewlines),
            confidence: 0.8, // Domain-based analysis has moderate confidence
            metadata: URLAnalysisResult.URLMetadata(
                title: nil,
                description: nil,
                keywords: nil,
                domain: domain
            )
        )
    }
    
    private func fetchURLMetadata(_ urlString: String) async throws -> URLAnalysisResult.URLMetadata {
        guard let url = URL(string: urlString) else {
            throw CategorizationError.invalidURL(urlString)
        }
        
        var request = URLRequest(url: url)
        request.setValue("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36", 
                        forHTTPHeaderField: "User-Agent")
        request.timeoutInterval = config.getAISettings().timeouts.urlFetch
        
        let (data, _) = try await URLSession.shared.data(for: request)
        
        guard let html = String(data: data, encoding: .utf8) else {
            throw CategorizationError.invalidResponse
        }
        
        return URLAnalysisResult.URLMetadata(
            title: extractHTMLTitle(from: html),
            description: extractMetaDescription(from: html),
            keywords: extractMetaKeywords(from: html),
            domain: url.host ?? urlString
        )
    }
    
    private func performContentAnalysis(url: String, metadata: URLAnalysisResult.URLMetadata) async throws -> URLAnalysisResult {
        let urlCategories = config.getURLCategories()
        let aiSettings = config.getAISettings()
        
        let prompt = """
        Analyze this webpage content and categorize it:
        
        URL: \(url)
        Title: \(metadata.title ?? "No title")
        Description: \(metadata.description ?? "No description")
        Keywords: \(metadata.keywords ?? "No keywords")
        
        Based on the actual page content, categorize this as one of:
        \(urlCategories.joined(separator: ", "))
        
        Return only the category name.
        """
        
        let category = try await aiService.makeRequest(
            prompt: prompt,
            maxTokens: aiSettings.maxTokens.urlAnalysis,
            timeout: aiSettings.timeouts.aiRequest
        )
        
        return URLAnalysisResult(
            category: category.trimmingCharacters(in: .whitespacesAndNewlines),
            confidence: 0.95, // Content-based analysis has high confidence
            metadata: metadata
        )
    }
    
    private func extractHTMLTitle(from html: String) -> String? {
        let titlePattern = "<title[^>]*>([^<]+)</title>"
        guard let regex = try? NSRegularExpression(pattern: titlePattern, options: .caseInsensitive),
              let match = regex.firstMatch(in: html, options: [], range: NSRange(location: 0, length: html.count)),
              let titleRange = Range(match.range(at: 1), in: html) else {
            return nil
        }
        return String(html[titleRange]).trimmingCharacters(in: .whitespacesAndNewlines)
    }
    
    private func extractMetaDescription(from html: String) -> String? {
        let descPattern = "<meta[^>]*name=[\"']description[\"'][^>]*content=[\"']([^\"']*)[\"'][^>]*>"
        guard let regex = try? NSRegularExpression(pattern: descPattern, options: .caseInsensitive),
              let match = regex.firstMatch(in: html, options: [], range: NSRange(location: 0, length: html.count)),
              let descRange = Range(match.range(at: 1), in: html) else {
            return nil
        }
        return String(html[descRange]).trimmingCharacters(in: .whitespacesAndNewlines)
    }
    
    private func extractMetaKeywords(from html: String) -> String? {
        let keywordsPattern = "<meta[^>]*name=[\"']keywords[\"'][^>]*content=[\"']([^\"']*)[\"'][^>]*>"
        guard let regex = try? NSRegularExpression(pattern: keywordsPattern, options: .caseInsensitive),
              let match = regex.firstMatch(in: html, options: [], range: NSRange(location: 0, length: html.count)),
              let keywordsRange = Range(match.range(at: 1), in: html) else {
            return nil
        }
        return String(html[keywordsRange]).trimmingCharacters(in: .whitespacesAndNewlines)
    }
}

// MARK: - Fallback URL Analyzer

class FallbackURLAnalyzer: URLAnalyzer {
    private let logger: Logger
    
    init(logger: Logger = ConsoleLogger()) {
        self.logger = logger
    }
    
    func analyzeURL(_ url: String) async throws -> URLAnalysisResult {
        logger.info("Using fallback URL analyzer for: \(url)")
        
        let domain = URL(string: url)?.host ?? url
        let category = categorizeByDomain(domain)
        
        return URLAnalysisResult(
            category: category,
            confidence: 0.5, // Low confidence for fallback
            metadata: URLAnalysisResult.URLMetadata(
                title: nil,
                description: nil,
                keywords: nil,
                domain: domain
            )
        )
    }
    
    func analyzeURLWithContent(_ url: String) async throws -> URLAnalysisResult {
        // Fallback doesn't support content analysis
        return try await analyzeURL(url)
    }
    
    private func categorizeByDomain(_ domain: String) -> String {
        let lowercaseDomain = domain.lowercased()
        
        // Basic domain-based categorization
        if lowercaseDomain.contains("shop") || lowercaseDomain.contains("store") || lowercaseDomain.contains("buy") {
            return "shopping"
        } else if lowercaseDomain.contains("news") || lowercaseDomain.contains("blog") {
            return "news"
        } else if lowercaseDomain.contains("learn") || lowercaseDomain.contains("edu") || lowercaseDomain.contains("course") {
            return "learning"
        } else {
            return "web_link"
        }
    }
}

// MARK: - Composite URL Analyzer

class CompositeURLAnalyzer: URLAnalyzer {
    private let primaryAnalyzer: URLAnalyzer
    private let fallbackAnalyzer: URLAnalyzer
    private let logger: Logger

    init(primaryAnalyzer: URLAnalyzer,
         fallbackAnalyzer: URLAnalyzer,
         logger: Logger = ConsoleLogger()) {
        self.primaryAnalyzer = primaryAnalyzer
        self.fallbackAnalyzer = fallbackAnalyzer
        self.logger = logger
    }

    func analyzeURL(_ url: String) async throws -> URLAnalysisResult {
        do {
            return try await primaryAnalyzer.analyzeURL(url)
        } catch {
            logger.warning("Primary URL analyzer failed, using fallback: \(error)")
            return try await fallbackAnalyzer.analyzeURL(url)
        }
    }

    func analyzeURLWithContent(_ url: String) async throws -> URLAnalysisResult {
        do {
            return try await primaryAnalyzer.analyzeURLWithContent(url)
        } catch {
            logger.warning("Primary URL content analyzer failed, using fallback: \(error)")
            return try await fallbackAnalyzer.analyzeURL(url)
        }
    }
}
