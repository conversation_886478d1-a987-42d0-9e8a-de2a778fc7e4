//
//  SubfolderAnalyzers.swift
//  Pebl
//
//  Created by AI Assistant on 6/17/25.
//

import Foundation

// MARK: - Pattern-Based Subfolder Analyzer

class PatternBasedSubfolderAnalyzer: SubfolderAnalyzer, PatternMatcher {
    private let config: ConfigurationProvider
    private let logger: Logger
    
    init(config: ConfigurationProvider, logger: Logger = ConsoleLogger()) {
        self.config = config
        self.logger = logger
    }
    
    func shouldCreateSubfolder(for message: String, in category: Category) async throws -> SubfolderSuggestion {
        let messageCount = category.getTotalMessageCountIncludingCompleted()
        
        // Don't create subfolders if the category doesn't have enough messages yet
        guard messageCount >= 5 else {
            return SubfolderSuggestion(
                shouldCreate: false,
                subfolderName: nil,
                confidence: 1.0,
                reasoning: "Category needs at least 5 messages before creating subfolders"
            )
        }
        
        // Try pattern matching first
        if let patternResult = findMatchingPattern(for: message, in: category) {
            return patternResult
        }
        
        // No pattern found
        return SubfolderSuggestion(
            shouldCreate: false,
            subfolderName: nil,
            confidence: 0.8,
            reasoning: "No matching pattern found for this message type"
        )
    }
    
    func analyzeExistingCategory(_ category: Category) async throws -> [String] {
        // Pattern-based analyzer doesn't provide category optimization
        // This would be handled by the AI-based analyzer
        return []
    }
    
    func findMatchingPattern(for message: String, in category: Category) -> SubfolderSuggestion? {
        let patterns = config.getSubfolderPatterns()
        let categoryName = category.name.lowercased()
        let messageLower = message.lowercased()
        
        // Find matching category pattern
        for (patternKey, patternConfig) in patterns {
            // Check if this pattern applies to the category
            let categoryMatches = patternConfig.triggers.contains { trigger in
                categoryName.contains(trigger)
            }
            
            guard categoryMatches else { continue }
            
            // Check if message matches any pattern rules
            for rule in patternConfig.patterns {
                let messageMatches = rule.keywords.contains { keyword in
                    messageLower.contains(keyword)
                }
                
                if messageMatches {
                    logger.debug("Pattern match found: \(rule.subfolder) for message in \(category.name)")
                    return SubfolderSuggestion(
                        shouldCreate: true,
                        subfolderName: rule.subfolder,
                        confidence: 0.9,
                        reasoning: "Matched pattern rule: \(rule.keywords.joined(separator: ", "))"
                    )
                }
            }
        }
        
        return nil
    }
}

// MARK: - AI-Powered Subfolder Analyzer

class AISubfolderAnalyzer: SubfolderAnalyzer {
    private let aiService: AIServiceProtocol
    private let cache: AnalysisCache
    private let config: ConfigurationProvider
    private let logger: Logger
    
    init(aiService: AIServiceProtocol,
         cache: AnalysisCache,
         config: ConfigurationProvider,
         logger: Logger = ConsoleLogger()) {
        self.aiService = aiService
        self.cache = cache
        self.config = config
        self.logger = logger
    }
    
    func shouldCreateSubfolder(for message: String, in category: Category) async throws -> SubfolderSuggestion {
        let messageCount = category.getTotalMessageCountIncludingCompleted()
        
        guard messageCount >= 5 else {
            return SubfolderSuggestion(
                shouldCreate: false,
                subfolderName: nil,
                confidence: 1.0,
                reasoning: "Category needs at least 5 messages before creating subfolders"
            )
        }
        
        // Check cache first
        let cacheKey = CacheKeyGenerator.subfolderKey(for: message, category: category.name)
        if let cachedResult = cache.getCachedResult(for: cacheKey, type: SubfolderSuggestion.self) {
            logger.debug("Using cached subfolder suggestion")
            return cachedResult
        }
        
        // Perform AI analysis
        let result = try await performAISubfolderAnalysis(message: message, category: category)
        
        // Cache the result
        let cachingConfig = config.getCachingConfig()
        if cachingConfig.enabled {
            cache.setCachedResult(result, for: cacheKey, ttl: cachingConfig.categoryAnalysisTTL)
        }
        
        return result
    }
    
    func analyzeExistingCategory(_ category: Category) async throws -> [String] {
        guard category.messages.count >= 8 else { return [] }
        
        let messages = category.messages.map { $0.text }
        let messageText = messages.joined(separator: "; ")
        let aiSettings = config.getAISettings()
        
        let prompt = """
        Analyze these messages in the "\(category.name)" category and suggest logical subfolders to organize them better.
        
        Messages: \(messageText)
        
        Look for natural patterns like:
        - Phases/stages of a process
        - Different types or categories of items
        - Priority levels or urgency
        - Time periods or deadlines
        - Locations or contexts
        - Status or completion stages
        
        Suggest 2-5 subfolder names that would help organize these messages logically. Each subfolder should be:
        - Broad enough to contain multiple messages
        - Clear and descriptive
        - Useful for future similar messages
        
        Return only the subfolder names, one per line, or "NO_SUGGESTIONS" if the messages don't show clear patterns.
        """
        
        let response = try await aiService.makeRequest(
            prompt: prompt,
            maxTokens: 100,
            timeout: aiSettings.timeouts.aiRequest
        )
        
        let trimmedResponse = response.trimmingCharacters(in: .whitespacesAndNewlines)
        
        if trimmedResponse == "NO_SUGGESTIONS" {
            return []
        }
        
        return trimmedResponse.components(separatedBy: .newlines)
            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            .filter { !$0.isEmpty }
    }
    
    private func performAISubfolderAnalysis(message: String, category: Category) async throws -> SubfolderSuggestion {
        let existingMessages = category.messages.map { $0.text }
        let existingSubfolders = category.subcategories.map { $0.name }
        let aiSettings = config.getAISettings()
        
        let existingMessagesText = existingMessages.prefix(10).joined(separator: "; ")
        let existingSubfoldersText = existingSubfolders.isEmpty ? "None" : existingSubfolders.joined(separator: ", ")
        
        let prompt = """
        Analyze if this new message should go into a subfolder within the "\(category.name)" category.
        
        Category: \(category.name)
        New Message: \(message)
        
        Existing messages in this category: \(existingMessagesText)
        Existing subfolders: \(existingSubfoldersText)
        
        RULES:
        1. Only suggest a subfolder if there's a clear, logical grouping that would help organize multiple similar items
        2. The subfolder name should be broad enough for future similar messages
        3. Consider natural groupings like: phases/stages, types/categories, priorities, time periods, locations, etc.
        4. If existing subfolders already cover this message, return the existing subfolder name
        5. Don't create subfolders for one-off items that won't have similar messages
        
        Examples of good subfolder patterns:
        - Book Writing: "Research", "Character Development", "Plot Outline", "Editing"
        - House Buying: "Research", "Viewing", "Financing", "Legal", "Moving"
        - Baby Planning: "Preparation", "Medical", "Shopping", "Nursery Setup"
        - Travel Planning: "Research", "Booking", "Packing", "Itinerary"
        - Project Management: "Planning", "Development", "Testing", "Documentation"
        
        Respond with either:
        - "NO_SUBFOLDER" if no subfolder is needed
        - Just the subfolder name if one should be created/used (e.g., "Research", "Planning Phase")
        
        Response:
        """
        
        let response = try await aiService.makeRequest(
            prompt: prompt,
            maxTokens: aiSettings.maxTokens.subfolderSuggestion,
            timeout: aiSettings.timeouts.aiRequest
        )
        
        let trimmedResponse = response.trimmingCharacters(in: .whitespacesAndNewlines)
        
        if trimmedResponse == "NO_SUBFOLDER" || trimmedResponse.isEmpty {
            return SubfolderSuggestion(
                shouldCreate: false,
                subfolderName: nil,
                confidence: 0.8,
                reasoning: "AI determined no subfolder is needed"
            )
        } else {
            return SubfolderSuggestion(
                shouldCreate: true,
                subfolderName: trimmedResponse,
                confidence: 0.85,
                reasoning: "AI suggested logical grouping based on content analysis"
            )
        }
    }
}

// MARK: - Composite Subfolder Analyzer

class CompositeSubfolderAnalyzer: SubfolderAnalyzer {
    private let patternAnalyzer: PatternBasedSubfolderAnalyzer
    private let aiAnalyzer: AISubfolderAnalyzer
    private let logger: Logger
    
    init(patternAnalyzer: PatternBasedSubfolderAnalyzer,
         aiAnalyzer: AISubfolderAnalyzer,
         logger: Logger = ConsoleLogger()) {
        self.patternAnalyzer = patternAnalyzer
        self.aiAnalyzer = aiAnalyzer
        self.logger = logger
    }
    
    func shouldCreateSubfolder(for message: String, in category: Category) async throws -> SubfolderSuggestion {
        // Try pattern-based analysis first (faster)
        let patternResult = try await patternAnalyzer.shouldCreateSubfolder(for: message, in: category)
        
        if patternResult.shouldCreate {
            logger.debug("Using pattern-based subfolder suggestion")
            return patternResult
        }
        
        // Fall back to AI analysis for more complex cases
        logger.debug("Pattern analysis didn't find match, trying AI analysis")
        return try await aiAnalyzer.shouldCreateSubfolder(for: message, in: category)
    }
    
    func analyzeExistingCategory(_ category: Category) async throws -> [String] {
        // Use AI analyzer for category optimization
        return try await aiAnalyzer.analyzeExistingCategory(category)
    }
}
