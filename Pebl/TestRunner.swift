//
//  TestRunner.swift
//  Pebl
//
//  Created by AI Assistant on 6/17/25.
//

import Foundation

/// Simple test runner to demonstrate the AI categorization system
class TestRunner {
    
    /// Run all tests and print results to console
    static func runAllTests() {
        print("🚀 Starting Pebl AI Categorization Tests")
        print(String(repeating: "=", count: 50))

        // Run the simple AI model tests
        SimpleAIModelTests.runTests()

        print("\n\n✅ All tests completed!")
        print("Check the console output above to see the categorization results.")
    }

    /// Run comprehensive edge case tests (Senior Engineer Level)
    static func runEdgeCaseTests() {
        EdgeCaseTests.runAllEdgeCaseTests()
    }

    /// Run smart categorization tests (new categories + edge case handling)
    static func runSmartCategorizationTests() {
        SmartCategorizationTests.runSmartCategorizationTests()
    }

    /// Demonstrate smart behavior
    static func demonstrateSmartBehavior() {
        SmartCategorizationTests.demonstrateSmartBehavior()
    }

    /// Test message parsing for modern UI
    static func testMessageParsing() {
        print("🎨 MODERN UI MESSAGE PARSING TESTS")
        print(String(repeating: "=", count: 60))

        let testCases = [
            ("Movies to Watch", [
                "Watch The Dark Knight recommended by <PERSON>",
                "See Inception on Netflix tonight",
                "Rahul recommended Prestige",
                "Watch Parasite - Korean film",
                "The Godfather from IMDB top 250"
            ]),
            ("Shopping", [
                "Buy iPhone 15 Pro from Apple Store",
                "Get groceries for dinner party",
                "Order Nike shoes size 10",
                "Purchase book for book club",
                "Buy urgent: milk and bread"
            ]),
            ("To-Read", [
                "Read Atomic Habits by James Clear",
                "Finish 1984 for book club",
                "Start The Hobbit recommended by Sarah",
                "Read research paper on AI",
                "Book: Dune by Frank Herbert"
            ])
        ]

        for (categoryName, messages) in testCases {
            print("\n📁 Category: \(categoryName)")
            print(String(repeating: "-", count: 40))

            for (index, messageText) in messages.enumerated() {
                let message = Message(text: messageText, categoryName: categoryName)

                print("\n\(index + 1). Original: \"\(messageText)\"")
                print("   Main: \"\(message.mainMessage)\"")
                if let subcontent = message.subcontent {
                    print("   Sub:  \"\(subcontent)\"")
                }
            }
        }

        print("\n\n✅ Message parsing tests completed!")
        print("Modern UI will display clean main messages with contextual subcontent.")
    }

    /// Run both normal and edge case tests
    static func runComprehensiveTests() {
        print("🚀 Starting Comprehensive Pebl AI Tests")
        print(String(repeating: "=", count: 60))

        // Run normal tests first
        SimpleAIModelTests.runTests()

        print("\n" + String(repeating: "=", count: 60))

        // Run smart categorization tests
        SmartCategorizationTests.runSmartCategorizationTests()

        print("\n" + String(repeating: "=", count: 60))

        // Run edge case tests
        EdgeCaseTests.runAllEdgeCaseTests()

        print("\n\n🎉 ALL COMPREHENSIVE TESTS COMPLETED!")
        print("The system has been thoroughly tested for robustness.")
    }
    
    /// Quick test with a single message
    static func quickTest() {
        print("⚡ Quick Test")
        print(String(repeating: "-", count: 20))
        
        let aiModel = AIModel()
        let categoryManager = CategoryManager()
        
        // Add a default category
        _ = categoryManager.addRootCategory(name: "Shopping", sfSymbol: "cart")
        
        // Test a simple message
        let message = "Buy iPhone 15 Pro"
        let availableCategories = categoryManager.getAllCategoryNames()
        let category = aiModel.categorizeMessage(message, availableCategories: availableCategories)
        
        print("Message: \"\(message)\"")
        print("Category: \(category)")
        
        // Test URL analysis
        let urls = aiModel.extractURLsFromMessage("Check out https://apple.com/iphone")
        if !urls.isEmpty {
            let urlCategory = aiModel.analyzeURLContent(urls[0])
            print("URL Category: \(urlCategory)")
        }
        
        print("✅ Quick test completed!")
    }
}

// MARK: - Usage Examples

/*

 To run tests in your app, you can call:

 // Run basic functionality tests
 TestRunner.runAllTests()

 // Run smart categorization tests (new categories + edge cases)
 TestRunner.runSmartCategorizationTests()

 // Test modern UI message parsing
 TestRunner.testMessageParsing()

 // Demonstrate smart behavior
 TestRunner.demonstrateSmartBehavior()

 // Run comprehensive edge case tests (Senior Engineer Level)
 TestRunner.runEdgeCaseTests()

 // Run all tests (normal + smart + edge cases)
 TestRunner.runComprehensiveTests()

 // Or run a quick test
 TestRunner.quickTest()

 // Test individual components
 let aiModel = AIModel()

 // Should use existing category
 let result1 = aiModel.categorizeMessage("Buy groceries", availableCategories: ["Shopping", "To-Do"])
 print("Existing: \(result1)")  // → "Shopping"

 // Should create new category
 let result2 = aiModel.categorizeMessage("Research wedding venues", availableCategories: ["Shopping", "To-Do"])
 print("New: \(result2)")  // → "Wedding Planning"

 // Should handle edge case gracefully
 let result3 = aiModel.categorizeMessage("seven", availableCategories: ["Shopping", "To-Do"])
 print("Edge case: \(result3)")  // → "To-Do" (not an AI explanation!)

 */
