//
//  ToggleTestView.swift
//  Pebl
//
//  Created by AI Assistant on 6/18/25.
//

import SwiftUI

/// Test view to verify message toggle functionality works correctly
struct ToggleTestView: View {
    @StateObject private var categoryManager = CategoryManager()
    @State private var testCategory: Category?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                if let category = testCategory {
                    Text("Test Category: \(category.name)")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("Messages: \(category.messages.count)")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    // Modern message list
                    ScrollView {
                        LazyVStack(spacing: 8) {
                            ForEach($category.messages) { $message in
                                ModernMessageView(message: $message) {
                                    print("🔄 Toggling message: \(message.text)")
                                    category.toggleMessageCompletion(withId: message.id)
                                    print("✅ Message is now: \(message.isCompleted ? "completed" : "active")")
                                }
                                .contextMenu {
                                    <PERSON><PERSON>("Delete", role: .destructive) {
                                        category.removeMessage(withId: message.id)
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, 16)
                    }
                    
                    // Test buttons
                    VStack(spacing: 12) {
                        <PERSON><PERSON>("Add Test Message") {
                            let testMessages = [
                                "Watch The Dark Knight recommended by John",
                                "Buy iPhone 15 Pro from Apple Store",
                                "Read Atomic Habits by James Clear",
                                "Complete quarterly report by Friday urgent",
                                "Schedule dentist appointment with Dr. Smith"
                            ]
                            
                            if let randomMessage = testMessages.randomElement() {
                                category.addMessage(randomMessage)
                            }
                        }
                        .buttonStyle(.borderedProminent)
                        
                        Button("Clear All Messages") {
                            category.messages.removeAll()
                        }
                        .buttonStyle(.bordered)
                        
                        Button("Toggle All Messages") {
                            for message in category.messages {
                                category.toggleMessageCompletion(withId: message.id)
                            }
                        }
                        .buttonStyle(.bordered)
                    }
                    .padding()
                    
                } else {
                    Text("Setting up test category...")
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("Toggle Test")
            .onAppear {
                setupTestCategory()
            }
        }
    }
    
    private func setupTestCategory() {
        // Create a test category with some sample messages
        let category = categoryManager.addRootCategory(name: "Test Category", sfSymbol: "checkmark.circle")
        
        // Add some test messages
        let testMessages = [
            "Watch The Dark Knight recommended by John",
            "Buy iPhone 15 Pro from Apple Store", 
            "Read Atomic Habits by James Clear",
            "Complete quarterly report by Friday urgent"
        ]
        
        for messageText in testMessages {
            category.addMessage(messageText)
        }
        
        self.testCategory = category
    }
}

/// Simplified test view to isolate the toggle issue
struct SimpleToggleTestView: View {
    @State private var messages: [Message] = [
        Message(text: "Watch The Dark Knight recommended by John", categoryName: "Movies to Watch"),
        Message(text: "Buy iPhone 15 Pro from Apple Store", categoryName: "Shopping"),
        Message(text: "Read Atomic Habits by James Clear", categoryName: "To-Read")
    ]
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Simple Toggle Test")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("Testing direct message toggle without category")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                ScrollView {
                    LazyVStack(spacing: 8) {
                        ForEach($messages) { $message in
                            ModernMessageView(message: $message) {
                                print("🔄 Direct toggle: \(message.text)")
                                message.isCompleted.toggle()
                                print("✅ Message is now: \(message.isCompleted ? "completed" : "active")")
                            }
                        }
                    }
                    .padding(.horizontal, 16)
                }
                
                // Status display
                VStack(alignment: .leading, spacing: 8) {
                    Text("Message Status:")
                        .font(.headline)
                    
                    ForEach(messages.indices, id: \.self) { index in
                        HStack {
                            Text("\(index + 1).")
                            Text(messages[index].mainMessage)
                            Spacer()
                            Text(messages[index].isCompleted ? "✅ Done" : "⏳ Active")
                                .foregroundColor(messages[index].isCompleted ? .green : .orange)
                        }
                        .font(.caption)
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
                
                Spacer()
            }
            .padding()
            .navigationTitle("Simple Test")
        }
    }
}

/// Debug view to show what's happening with the toggle
struct DebugToggleView: View {
    @StateObject private var category = Category(name: "Debug Category", sfSymbol: "bug")
    @State private var toggleCount = 0
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Debug Toggle View")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("Toggle count: \(toggleCount)")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                if !category.messages.isEmpty {
                    ScrollView {
                        LazyVStack(spacing: 8) {
                            ForEach($category.messages) { $message in
                                VStack(alignment: .leading, spacing: 8) {
                                    // Debug info
                                    Text("ID: \(message.id.uuidString.prefix(8))")
                                        .font(.caption2)
                                        .foregroundColor(.secondary)
                                    
                                    Text("Completed: \(message.isCompleted ? "YES" : "NO")")
                                        .font(.caption2)
                                        .foregroundColor(message.isCompleted ? .green : .red)
                                    
                                    // Modern message view
                                    ModernMessageView(message: $message) {
                                        print("🔄 Toggle called for: \(message.text)")
                                        print("   Before: \(message.isCompleted)")
                                        
                                        category.toggleMessageCompletion(withId: message.id)
                                        toggleCount += 1
                                        
                                        print("   After: \(message.isCompleted)")
                                        print("   Toggle count: \(toggleCount)")
                                    }
                                }
                                .padding(8)
                                .background(Color(.systemGray6))
                                .cornerRadius(8)
                            }
                        }
                        .padding(.horizontal, 16)
                    }
                } else {
                    Text("No messages yet")
                        .foregroundColor(.secondary)
                }
                
                Button("Add Debug Message") {
                    let message = "Debug message \(category.messages.count + 1)"
                    category.addMessage(message)
                }
                .buttonStyle(.borderedProminent)
                
                Spacer()
            }
            .padding()
            .navigationTitle("Debug")
        }
    }
}

#Preview("Toggle Test") {
    ToggleTestView()
}

#Preview("Simple Test") {
    SimpleToggleTestView()
}

#Preview("Debug Test") {
    DebugToggleView()
}
