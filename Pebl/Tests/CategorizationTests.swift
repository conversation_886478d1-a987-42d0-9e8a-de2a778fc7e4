//
//  CategorizationTests.swift
//  Pebl
//
//  Created by AI Assistant on 6/17/25.
//

import XCTest
@testable import Pebl

class CategorizationTests: XCTestCase {
    
    var container: TestDependencyContainer!
    var factory: CategorizationServiceFactory!
    
    override func setUp() {
        super.setUp()
        container = TestDependencyContainer()
        setupMocks()
        factory = container.createTestFactory()
    }
    
    override func tearDown() {
        container = nil
        factory = nil
        super.tearDown()
    }
    
    private func setupMocks() {
        // Register mock dependencies
        container.registerMock(Logger.self, mock: MockLogger())
        container.registerMock(AnalysisCache.self, mock: MockCache())
        container.registerMock(AIServiceProtocol.self, mock: MockAIService())
        container.registerMock(ConfigurationProvider.self, mock: MockConfigurationProvider())
    }
    
    // MARK: - URL Analyzer Tests
    
    func testURLAnalyzer_ValidURL_ReturnsCategory() async throws {
        let urlAnalyzer = factory.createURLAnalyzer()
        
        let result = try await urlAnalyzer.analyzeURL("https://www.example-shop.com/products")
        
        XCTAssertEqual(result.category, "shopping")
        XCTAssertGreaterThan(result.confidence, 0.0)
        XCTAssertNotNil(result.metadata)
    }
    
    func testURLAnalyzer_InvalidURL_ThrowsError() async {
        let urlAnalyzer = factory.createURLAnalyzer()
        
        do {
            _ = try await urlAnalyzer.analyzeURL("invalid-url")
            XCTFail("Expected error for invalid URL")
        } catch CategorizationError.invalidURL {
            // Expected error
        } catch {
            XCTFail("Unexpected error type: \(error)")
        }
    }
    
    func testURLAnalyzer_WithContent_ReturnsHigherConfidence() async throws {
        let urlAnalyzer = factory.createURLAnalyzer()
        
        let basicResult = try await urlAnalyzer.analyzeURL("https://www.example.com")
        let contentResult = try await urlAnalyzer.analyzeURLWithContent("https://www.example.com")
        
        XCTAssertGreaterThanOrEqual(contentResult.confidence, basicResult.confidence)
    }
    
    // MARK: - Message Categorizer Tests
    
    func testMessageCategorizer_ValidMessage_ReturnsCategory() async throws {
        let categorizer = factory.createMessageCategorizer()
        let categories = ["Shopping", "To-Do", "Movies"]
        
        let result = try await categorizer.categorizeMessage("Buy groceries", availableCategories: categories)
        
        XCTAssertTrue(categories.contains(result))
    }
    
    func testMessageCategorizer_EmptyCategories_ThrowsError() async {
        let categorizer = factory.createMessageCategorizer()
        
        do {
            _ = try await categorizer.categorizeMessage("Test message", availableCategories: [])
            XCTFail("Expected error for empty categories")
        } catch CategorizationError.invalidConfiguration {
            // Expected error
        } catch {
            XCTFail("Unexpected error type: \(error)")
        }
    }
    
    func testMessageCategorizer_WithContext_ReturnsCompleteResult() async throws {
        let categorizer = factory.createMessageCategorizer()
        let categoryManager = MockCategoryManager()
        
        let result = try await categorizer.categorizeMessageWithContext("Test message", categoryManager: categoryManager)
        
        XCTAssertNotNil(result)
        XCTAssertGreaterThan(result.confidence, 0.0)
    }
    
    // MARK: - Subfolder Analyzer Tests
    
    func testSubfolderAnalyzer_InsufficientMessages_ReturnsFalse() async throws {
        let analyzer = factory.createSubfolderAnalyzer()
        let category = createTestCategory(messageCount: 3)
        
        let result = try await analyzer.shouldCreateSubfolder(for: "Test message", in: category)
        
        XCTAssertFalse(result.shouldCreate)
        XCTAssertNil(result.subfolderName)
    }
    
    func testSubfolderAnalyzer_SufficientMessages_MayReturnTrue() async throws {
        let analyzer = factory.createSubfolderAnalyzer()
        let category = createTestCategory(messageCount: 10)
        
        let result = try await analyzer.shouldCreateSubfolder(for: "Comedy movie", in: category)
        
        // Result depends on the mock implementation
        XCTAssertNotNil(result)
        XCTAssertGreaterThan(result.confidence, 0.0)
    }
    
    func testSubfolderAnalyzer_CategoryOptimization_ReturnsValidSuggestions() async throws {
        let analyzer = factory.createSubfolderAnalyzer()
        let category = createTestCategory(messageCount: 15)
        
        let suggestions = try await analyzer.analyzeExistingCategory(category)
        
        XCTAssertTrue(suggestions.count <= 5) // Should not suggest too many subfolders
    }
    
    // MARK: - Symbol Generator Tests
    
    func testSymbolGenerator_ValidCategory_ReturnsSymbol() async throws {
        let generator = factory.createSymbolGenerator()
        
        let symbol = try await generator.getSFSymbol(for: "Shopping")
        
        XCTAssertFalse(symbol.isEmpty)
        XCTAssertFalse(symbol.contains("\"")) // Should not contain quotes
    }
    
    // MARK: - Integration Tests
    
    func testRefactoredAIModel_BackwardCompatibility() {
        let model = RefactoredAIModel(factory: factory)
        
        let result = model.categorizeMessage("Test message", availableCategories: ["Test"])
        
        XCTAssertFalse(result.isEmpty)
    }
    
    func testRefactoredAIModel_AsyncAPI() async throws {
        let model = RefactoredAIModel(factory: factory)
        
        let result = try await model.categorizeMessageAsync("Test message", availableCategories: ["Test"])
        
        XCTAssertFalse(result.isEmpty)
    }
    
    // MARK: - Error Handling Tests
    
    func testErrorHandling_NetworkError_HandledGracefully() async {
        let mockAIService = MockAIServiceWithErrors()
        container.registerMock(AIServiceProtocol.self, mock: mockAIService)
        
        let categorizer = factory.createMessageCategorizer()
        
        do {
            _ = try await categorizer.categorizeMessage("Test", availableCategories: ["Test"])
            XCTFail("Expected network error")
        } catch CategorizationError.networkError {
            // Expected error
        } catch {
            XCTFail("Unexpected error type: \(error)")
        }
    }
    
    func testErrorHandling_RateLimitError_HandledGracefully() async {
        let mockAIService = MockAIServiceWithRateLimit()
        container.registerMock(AIServiceProtocol.self, mock: mockAIService)
        
        let categorizer = factory.createMessageCategorizer()
        
        do {
            _ = try await categorizer.categorizeMessage("Test", availableCategories: ["Test"])
            XCTFail("Expected rate limit error")
        } catch CategorizationError.rateLimitExceeded {
            // Expected error
        } catch {
            XCTFail("Unexpected error type: \(error)")
        }
    }
    
    // MARK: - Performance Tests
    
    func testPerformance_Categorization() {
        let model = RefactoredAIModel(factory: factory)
        let categories = ["Shopping", "To-Do", "Movies", "Reading", "Work"]
        
        measure {
            for i in 0..<100 {
                _ = model.categorizeMessage("Test message \(i)", availableCategories: categories)
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func createTestCategory(messageCount: Int) -> Category {
        let category = Category(name: "Test Category", sfSymbol: "folder.fill")
        
        for i in 0..<messageCount {
            category.addMessage("Test message \(i)")
        }
        
        return category
    }
}

// MARK: - Mock Implementations

class MockAIService: AIServiceProtocol {
    func makeRequest(prompt: String, maxTokens: Int, timeout: TimeInterval) async throws -> String {
        // Simulate AI response based on prompt content
        if prompt.contains("shopping") || prompt.contains("buy") {
            return "shopping"
        } else if prompt.contains("movie") || prompt.contains("watch") {
            return "entertainment"
        } else if prompt.contains("SF Symbol") {
            return "cart.fill"
        } else {
            return "To-Do"
        }
    }
}

class MockAIServiceWithErrors: AIServiceProtocol {
    func makeRequest(prompt: String, maxTokens: Int, timeout: TimeInterval) async throws -> String {
        throw CategorizationError.networkError(NSError(domain: "Test", code: 1, userInfo: nil))
    }
}

class MockAIServiceWithRateLimit: AIServiceProtocol {
    func makeRequest(prompt: String, maxTokens: Int, timeout: TimeInterval) async throws -> String {
        throw CategorizationError.rateLimitExceeded
    }
}

class MockConfigurationProvider: ConfigurationProvider {
    func getSubfolderPatterns() -> [String: SubfolderPatternConfig] {
        return [:]
    }
    
    func getURLCategories() -> [String] {
        return ["shopping", "entertainment", "news", "learning"]
    }
    
    func getDefaultCategories() -> [DefaultCategoryConfig] {
        return [
            DefaultCategoryConfig(name: "To-Do", sfSymbol: "checkmark.square"),
            DefaultCategoryConfig(name: "Shopping", sfSymbol: "cart")
        ]
    }
    
    func getAISettings() -> AISettings {
        return AISettings(
            model: "gpt-4",
            maxTokens: MaxTokensConfig(categorization: 30, subfolderSuggestion: 50, urlAnalysis: 20, sfSymbol: 20),
            timeouts: TimeoutsConfig(urlFetch: 10.0, aiRequest: 30.0),
            retryAttempts: 2
        )
    }
    
    func getCachingConfig() -> CachingConfig {
        return CachingConfig(enabled: false, urlAnalysisTTL: 3600, categoryAnalysisTTL: 1800, maxCacheSize: 100)
    }
}

class MockCategoryManager: CategoryManagerProtocol {
    var rootCategories: [Category] = []
    
    init() {
        rootCategories = [
            Category(name: "To-Do", sfSymbol: "checkmark.square"),
            Category(name: "Shopping", sfSymbol: "cart")
        ]
    }
    
    func findCategory(named name: String) -> Category? {
        return rootCategories.first { $0.name == name }
    }
    
    func getAllCategoryNames() -> [String] {
        return rootCategories.map { $0.name }
    }
    
    func addRootCategory(name: String, sfSymbol: String) -> Category {
        let category = Category(name: name, sfSymbol: sfSymbol)
        rootCategories.append(category)
        return category
    }
    
    func removeRootCategory(_ category: Category) {
        rootCategories.removeAll { $0.id == category.id }
    }
}
