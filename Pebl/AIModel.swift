import Foundation

class AIModel {

    // MARK: - URL Content Analysis

    private func extractURLsFromMessage(_ message: String) -> [String] {
        let detector = try? NSDataDetector(types: NSTextCheckingResult.CheckingType.link.rawValue)
        let matches = detector?.matches(in: message, options: [], range: NSRange(location: 0, length: message.utf16.count))

        return matches?.compactMap { match in
            if let range = Range(match.range, in: message) {
                return String(message[range])
            }
            return nil
        } ?? []
    }

    private func analyzeURLContent(_ url: String) -> String {
        // Enhanced URL analysis with more comprehensive domain detection
        let lowercaseURL = url.lowercased()

        // E-commerce sites
        if lowercaseURL.contains("amazon") || lowercaseURL.contains("ebay") ||
           lowercaseURL.contains("shop") || lowercaseURL.contains("store") ||
           lowercaseURL.contains("buy") || lowercaseURL.contains("cart") ||
           lowercaseURL.contains("etsy") || lowercaseURL.contains("walmart") ||
           lowercaseURL.contains("target") || lowercaseURL.contains("bestbuy") {
            return "shopping"
        }

        // Entertainment sites
        if lowercaseURL.contains("netflix") || lowercaseURL.contains("youtube") ||
           lowercaseURL.contains("imdb") || lowercaseURL.contains("movie") ||
           lowercaseURL.contains("film") || lowercaseURL.contains("cinema") ||
           lowercaseURL.contains("hulu") || lowercaseURL.contains("disney") ||
           lowercaseURL.contains("spotify") || lowercaseURL.contains("music") {
            return "entertainment"
        }

        // News sites
        if lowercaseURL.contains("news") || lowercaseURL.contains("cnn") ||
           lowercaseURL.contains("bbc") || lowercaseURL.contains("reuters") ||
           lowercaseURL.contains("nytimes") || lowercaseURL.contains("wsj") ||
           lowercaseURL.contains("guardian") || lowercaseURL.contains("npr") {
            return "news"
        }

        // Social media
        if lowercaseURL.contains("twitter") || lowercaseURL.contains("facebook") ||
           lowercaseURL.contains("instagram") || lowercaseURL.contains("linkedin") ||
           lowercaseURL.contains("reddit") || lowercaseURL.contains("tiktok") ||
           lowercaseURL.contains("snapchat") || lowercaseURL.contains("discord") {
            return "social"
        }

        // Documentation/Learning
        if lowercaseURL.contains("docs") || lowercaseURL.contains("tutorial") ||
           lowercaseURL.contains("learn") || lowercaseURL.contains("course") ||
           lowercaseURL.contains("education") || lowercaseURL.contains("udemy") ||
           lowercaseURL.contains("coursera") || lowercaseURL.contains("khan") ||
           lowercaseURL.contains("stackoverflow") || lowercaseURL.contains("github") {
            return "learning"
        }

        // Work/Professional
        if lowercaseURL.contains("slack") || lowercaseURL.contains("teams") ||
           lowercaseURL.contains("zoom") || lowercaseURL.contains("calendar") ||
           lowercaseURL.contains("office") || lowercaseURL.contains("google.com/drive") {
            return "work"
        }

        // Health & Fitness
        if lowercaseURL.contains("health") || lowercaseURL.contains("fitness") ||
           lowercaseURL.contains("medical") || lowercaseURL.contains("doctor") ||
           lowercaseURL.contains("workout") || lowercaseURL.contains("nutrition") {
            return "health"
        }

        // Travel
        if lowercaseURL.contains("booking") || lowercaseURL.contains("airbnb") ||
           lowercaseURL.contains("expedia") || lowercaseURL.contains("travel") ||
           lowercaseURL.contains("flight") || lowercaseURL.contains("hotel") {
            return "travel"
        }

        return "web_link"
    }

    private func fetchURLTitle(_ urlString: String, completion: @escaping (String?) -> Void) {
        guard let url = URL(string: urlString) else {
            completion(nil)
            return
        }

        let task = URLSession.shared.dataTask(with: url) { data, response, error in
            guard let data = data, error == nil else {
                completion(nil)
                return
            }

            if let html = String(data: data, encoding: .utf8) {
                // Extract title from HTML
                let titlePattern = "<title[^>]*>([^<]+)</title>"
                if let regex = try? NSRegularExpression(pattern: titlePattern, options: .caseInsensitive),
                   let match = regex.firstMatch(in: html, options: [], range: NSRange(location: 0, length: html.count)),
                   let titleRange = Range(match.range(at: 1), in: html) {
                    let title = String(html[titleRange]).trimmingCharacters(in: .whitespacesAndNewlines)
                    completion(title)
                    return
                }
            }
            completion(nil)
        }
        task.resume()
    }

    func getSFSymbolForCategory(_ category: String) -> String {
        // Use ChatGPT API for SFSymbol match
        guard let apiKey = ProcessInfo.processInfo.environment["OPENAI_API_KEY"] else {
            fatalError("API key not found in environment variables")
        }
        let url = URL(string: "https://api.openai.com/v1/chat/completions")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")

        let prompt = "What is the most apt SF Symbol that can be used in SwiftUI App to represent the category \"\(category)\"? Only return the SF Symbol string that can be used in SwiftUI App as the value for systemName. No other formatting and nothing else."
        let parameters: [String: Any] = [
            "model": "gpt-4",
            "messages": [
            ["role": "system", "content": "I am a helpful Swift coding assistant that finds the most beautiful and apt symbols to represent different categories in a SwiftUI app. I will take the best call even in times of ambiguity and return only one most fitting SF Symbol string that can be directly used in application code. Please provide me with a category name and I will use my advanced algorithms to find the most fitting SF Symbol for your messages."],
            ["role": "user", "content": prompt]
            ],
            "max_tokens": 20
        ]

        do {
            let jsonData = try JSONSerialization.data(withJSONObject: parameters, options: [])
            request.httpBody = jsonData
        } catch {
            print("Error serializing JSON: \(error)")
            return "Error"
        }

        let semaphore = DispatchSemaphore(value: 0)
        var symbol = "folder.fill" // Default category

        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
            print("Error making API request: \(error)")
            } else if let data = data {
            do {
                if let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
                   let choices = json["choices"] as? [[String: Any]],
                   let message = choices.first?["message"] as? [String: Any],
                   let text = message["content"] as? String {
                    symbol = text.trimmingCharacters(in: .whitespacesAndNewlines)
                    let quoteCharacterSet = CharacterSet(charactersIn: "\"")
                    symbol = symbol.trimmingCharacters(in: quoteCharacterSet)
                }
            } catch {
                print("Error parsing JSON response: \(error)")
            }
            }
            semaphore.signal()
        }

        task.resume()
        semaphore.wait()
        return symbol
    }

    // MARK: - Context-Aware Categorization

    func categorizeMessageWithContext(_ message: String, categoryManager: CategoryManager) -> (category: Category?, shouldCreateSubfolder: Bool, subfolderName: String?) {
        let availableCategories = categoryManager.getAllCategoryNames()
        let categoryName = categorizeMessage(message, availableCategories: availableCategories)

        // Find the target category
        guard let targetCategory = categoryManager.findCategory(named: categoryName) else {
            // Category doesn't exist, will be created as root category
            return (nil, false, nil)
        }

        // Check if we should create a subfolder
        let subfolderResult = shouldCreateSubfolder(for: message, in: targetCategory)

        if subfolderResult.shouldCreate, let subfolderName = subfolderResult.subfolderName {
            // Check if subfolder already exists
            if let existingSubfolder = targetCategory.subcategories.first(where: { $0.name == subfolderName }) {
                return (existingSubfolder, false, nil)
            } else {
                return (targetCategory, true, subfolderName)
            }
        }

        return (targetCategory, false, nil)
    }

    // MARK: - Smart Subfolder Logic

    func shouldCreateSubfolder(for message: String, in category: Category) -> (shouldCreate: Bool, subfolderName: String?) {
        let messageCount = category.getTotalMessageCountIncludingCompleted()

        // Don't create subfolders if the category doesn't have enough messages yet
        guard messageCount >= 5 else { return (false, nil) }

        // Analyze if this message suggests a natural subcategory
        let categoryName = category.name.lowercased()
        let messageLower = message.lowercased()

        // Movies to Watch -> Genre-based subfolders
        if categoryName.contains("movie") || categoryName.contains("film") || categoryName.contains("watch") {
            if messageLower.contains("comedy") || messageLower.contains("funny") || messageLower.contains("laugh") {
                return (true, "Comedy")
            } else if messageLower.contains("action") || messageLower.contains("adventure") || messageLower.contains("thriller") {
                return (true, "Action & Adventure")
            } else if messageLower.contains("drama") || messageLower.contains("romantic") || messageLower.contains("romance") {
                return (true, "Drama & Romance")
            } else if messageLower.contains("horror") || messageLower.contains("scary") || messageLower.contains("thriller") {
                return (true, "Horror & Thriller")
            } else if messageLower.contains("documentary") || messageLower.contains("docu") {
                return (true, "Documentaries")
            }
        }

        // Shopping -> Category-based subfolders
        if categoryName.contains("shop") {
            if messageLower.contains("cloth") || messageLower.contains("shirt") || messageLower.contains("dress") ||
               messageLower.contains("shoe") || messageLower.contains("fashion") {
                return (true, "Clothing & Fashion")
            } else if messageLower.contains("electronic") || messageLower.contains("phone") || messageLower.contains("computer") ||
                      messageLower.contains("gadget") || messageLower.contains("tech") {
                return (true, "Electronics")
            } else if messageLower.contains("book") || messageLower.contains("read") {
                return (true, "Books")
            } else if messageLower.contains("food") || messageLower.contains("grocery") || messageLower.contains("kitchen") {
                return (true, "Food & Grocery")
            } else if messageLower.contains("home") || messageLower.contains("furniture") || messageLower.contains("decor") {
                return (true, "Home & Garden")
            }
        }

        // To-Do -> Priority or type-based subfolders
        if categoryName.contains("to-do") || categoryName.contains("todo") || categoryName.contains("task") {
            if messageLower.contains("urgent") || messageLower.contains("asap") || messageLower.contains("important") {
                return (true, "Urgent")
            } else if messageLower.contains("work") || messageLower.contains("office") || messageLower.contains("meeting") {
                return (true, "Work")
            } else if messageLower.contains("personal") || messageLower.contains("home") || messageLower.contains("family") {
                return (true, "Personal")
            }
        }

        // To-Read -> Type-based subfolders
        if categoryName.contains("read") {
            if messageLower.contains("article") || messageLower.contains("blog") || messageLower.contains("news") {
                return (true, "Articles & Blogs")
            } else if messageLower.contains("book") || messageLower.contains("novel") {
                return (true, "Books")
            } else if messageLower.contains("research") || messageLower.contains("paper") || messageLower.contains("study") {
                return (true, "Research & Papers")
            }
        }

        return (false, nil)
    }

    func categorizeMessage(_ message: String, availableCategories: [String]) -> String {
        guard !availableCategories.isEmpty else { return "Uncategorized" }

        // Analyze URLs in the message for better context
        let urls = extractURLsFromMessage(message)
        let urlContext = urls.map { analyzeURLContent($0) }.joined(separator: ", ")

        // Use ChatGPT API for categorization
        guard let apiKey = ProcessInfo.processInfo.environment["OPENAI_API_KEY"] else {
            fatalError("API key not found in environment variables")
        }
        let url = URL(string: "https://api.openai.com/v1/chat/completions")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")

        let urlAnalysis = urls.isEmpty ? "" : "\n\nURL Analysis: The message contains URLs that appear to be related to: \(urlContext)"

        let prompt = """
        Categorize the following message into one of these existing categories: \(availableCategories.joined(separator: ", ")).

        IMPORTANT RULES:
        1. STRONGLY prefer existing categories over creating new ones
        2. For movies/shows: Consider if they fit into existing entertainment categories or if a genre-specific subcategory would be better
        3. For shopping items: Use "Shopping" unless there's a more specific existing category
        4. For URLs: Consider the website content and purpose, not just the URL itself
        5. For tasks/reminders: Use "To-Do" unless there's a more specific category
        6. For books/articles: Use "To-Read" unless there's a more specific category
        7. Only create a NEW category if the message truly doesn't fit any existing category AND represents a significant new area

        If you must create a new category, make it:
        - Broad enough for future similar items
        - Clear and unambiguous
        - Properly capitalized (e.g., "Health & Fitness", "Work Projects")

        Return ONLY the category name, nothing else.\(urlAnalysis)

        Message: \(message)
        """

        let parameters: [String: Any] = [
            "model": "gpt-4",
            "messages": [
                ["role": "system", "content": "You are an intelligent categorization assistant for a personal organization app. Your goal is to help users organize their messages, links, tasks, and notes into meaningful categories. You excel at understanding context, recognizing patterns, and making smart decisions about where content belongs. You strongly prefer using existing categories and only create new ones when absolutely necessary. You understand that good organization means having the right balance - not too many categories (overwhelming) but enough to be useful."],
                ["role": "user", "content": prompt]
            ],
            "max_tokens": 30
        ]
        
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: parameters, options: [])
            request.httpBody = jsonData
        } catch {
            print("Error serializing JSON: \(error)")
            return "Error"
        }
        
        let semaphore = DispatchSemaphore(value: 0)
        var category = "To-Do" // Default category
        
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
            print("Error making API request: \(error)")
            } else if let data = data {
            do {
                if let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
                   let choices = json["choices"] as? [[String: Any]],
                   let message = choices.first?["message"] as? [String: Any],
                   let text = message["content"] as? String {
                    category = text.trimmingCharacters(in: .whitespacesAndNewlines)
                    let quoteCharacterSet = CharacterSet(charactersIn: "\"")
                    category = category.trimmingCharacters(in: quoteCharacterSet)
                }
            } catch {
                print("Error parsing JSON response: \(error)")
            }
            }
            semaphore.signal()
        }
        
        task.resume()
        semaphore.wait()
        return category
    }
}
