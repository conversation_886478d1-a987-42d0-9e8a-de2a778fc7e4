import Foundation

class AIModel {

    // MARK: - URL Content Analysis

    func extractURLsFromMessage(_ message: String) -> [String] {
        let detector = try? NSDataDetector(types: NSTextCheckingResult.CheckingType.link.rawValue)
        let matches = detector?.matches(in: message, options: [], range: NSRange(location: 0, length: message.utf16.count))

        return matches?.compactMap { match in
            if let range = Range(match.range, in: message) {
                return String(message[range])
            }
            return nil
        } ?? []
    }

    func analyzeURLContent(_ url: String) -> String {
        // Use AI to dynamically analyze URL content instead of predefined lists
        guard let apiKey = ProcessInfo.processInfo.environment["OPENAI_API_KEY"] else {
            return "web_link"
        }

        let urlToAnalyze = URL(string: url)
        let domain = urlToAnalyze?.host ?? url

        let urlRequest = URL(string: "https://api.openai.com/v1/chat/completions")!
        var request = URLRequest(url: urlRequest)
        request.httpMethod = "POST"
        request.addValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")

        let prompt = """
        Analyze this URL and determine what category of content it likely represents: \(url)

        Domain: \(domain)

        Based on the URL structure, domain name, and path, categorize this as one of:
        - shopping (e-commerce, retail, buying/selling)
        - entertainment (movies, music, games, streaming, social media)
        - news (journalism, current events, media outlets)
        - learning (education, tutorials, documentation, courses)
        - work (productivity, business tools, professional services)
        - health (medical, fitness, wellness, nutrition)
        - travel (booking, planning, transportation, accommodation)
        - finance (banking, investing, payments, financial services)
        - food (recipes, restaurants, cooking, dining)
        - technology (tech news, software, development, gadgets)
        - lifestyle (fashion, home, personal interests)
        - web_link (if none of the above fit well)

        Consider:
        - Domain name patterns and keywords
        - URL path structure
        - Common website purposes
        - Industry-standard domain conventions

        Return only the category name, nothing else.
        """

        let parameters: [String: Any] = [
            "model": "gpt-4",
            "messages": [
                ["role": "system", "content": "You are an expert at analyzing URLs and understanding website purposes. You can identify the type of content or service a website provides based on its domain name, URL structure, and common web patterns."],
                ["role": "user", "content": prompt]
            ],
            "max_tokens": 20
        ]

        do {
            let jsonData = try JSONSerialization.data(withJSONObject: parameters, options: [])
            request.httpBody = jsonData
        } catch {
            print("Error serializing JSON: \(error)")
            return "web_link"
        }

        let semaphore = DispatchSemaphore(value: 0)
        var result = "web_link"

        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                print("Error making API request: \(error)")
            } else if let data = data {
                do {
                    if let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
                       let choices = json["choices"] as? [[String: Any]],
                       let message = choices.first?["message"] as? [String: Any],
                       let text = message["content"] as? String {
                        result = text.trimmingCharacters(in: .whitespacesAndNewlines)
                    }
                } catch {
                    print("Error parsing JSON response: \(error)")
                }
            }
            semaphore.signal()
        }

        task.resume()
        semaphore.wait()
        return result
    }

    private func analyzeURLWithContent(_ urlString: String, completion: @escaping (String) -> Void) {
        guard let url = URL(string: urlString) else {
            completion("web_link")
            return
        }

        // Create a URLRequest with proper headers to avoid being blocked
        var request = URLRequest(url: url)
        request.setValue("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36", forHTTPHeaderField: "User-Agent")
        request.timeoutInterval = 10.0

        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            guard let data = data, error == nil else {
                // Fallback to domain-based analysis if content fetch fails
                completion(self.analyzeURLContent(urlString))
                return
            }

            if let html = String(data: data, encoding: .utf8) {
                // Extract title and meta description for better analysis
                let title = self.extractHTMLTitle(from: html)
                let description = self.extractMetaDescription(from: html)
                let keywords = self.extractMetaKeywords(from: html)

                // Use AI to analyze the actual page content
                self.analyzePageContent(url: urlString, title: title, description: description, keywords: keywords) { category in
                    completion(category)
                }
            } else {
                completion(self.analyzeURLContent(urlString))
            }
        }
        task.resume()
    }

    private func extractHTMLTitle(from html: String) -> String? {
        let titlePattern = "<title[^>]*>([^<]+)</title>"
        if let regex = try? NSRegularExpression(pattern: titlePattern, options: .caseInsensitive),
           let match = regex.firstMatch(in: html, options: [], range: NSRange(location: 0, length: html.count)),
           let titleRange = Range(match.range(at: 1), in: html) {
            return String(html[titleRange]).trimmingCharacters(in: .whitespacesAndNewlines)
        }
        return nil
    }

    private func extractMetaDescription(from html: String) -> String? {
        let descPattern = "<meta[^>]*name=[\"']description[\"'][^>]*content=[\"']([^\"']*)[\"'][^>]*>"
        if let regex = try? NSRegularExpression(pattern: descPattern, options: .caseInsensitive),
           let match = regex.firstMatch(in: html, options: [], range: NSRange(location: 0, length: html.count)),
           let descRange = Range(match.range(at: 1), in: html) {
            return String(html[descRange]).trimmingCharacters(in: .whitespacesAndNewlines)
        }
        return nil
    }

    private func extractMetaKeywords(from html: String) -> String? {
        let keywordsPattern = "<meta[^>]*name=[\"']keywords[\"'][^>]*content=[\"']([^\"']*)[\"'][^>]*>"
        if let regex = try? NSRegularExpression(pattern: keywordsPattern, options: .caseInsensitive),
           let match = regex.firstMatch(in: html, options: [], range: NSRange(location: 0, length: html.count)),
           let keywordsRange = Range(match.range(at: 1), in: html) {
            return String(html[keywordsRange]).trimmingCharacters(in: .whitespacesAndNewlines)
        }
        return nil
    }

    private func analyzePageContent(url: String, title: String?, description: String?, keywords: String?, completion: @escaping (String) -> Void) {
        guard let apiKey = ProcessInfo.processInfo.environment["OPENAI_API_KEY"] else {
            completion("web_link")
            return
        }

        let urlRequest = URL(string: "https://api.openai.com/v1/chat/completions")!
        var request = URLRequest(url: urlRequest)
        request.httpMethod = "POST"
        request.addValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")

        let titleText = title ?? "No title"
        let descText = description ?? "No description"
        let keywordsText = keywords ?? "No keywords"

        let prompt = """
        Analyze this webpage content and categorize it:

        URL: \(url)
        Title: \(titleText)
        Description: \(descText)
        Keywords: \(keywordsText)

        Based on the actual page content, categorize this as:
        - shopping (e-commerce, products, retail)
        - entertainment (movies, music, games, streaming, social media)
        - news (journalism, current events, articles)
        - learning (education, tutorials, documentation, courses)
        - work (productivity, business tools, professional services)
        - health (medical, fitness, wellness, nutrition)
        - travel (booking, planning, transportation)
        - finance (banking, investing, payments)
        - food (recipes, restaurants, cooking)
        - technology (tech news, software, development)
        - lifestyle (fashion, home, personal interests)
        - web_link (if none fit well)

        Return only the category name.
        """

        let parameters: [String: Any] = [
            "model": "gpt-4",
            "messages": [
                ["role": "system", "content": "You are an expert at analyzing webpage content and understanding the purpose and category of websites based on their actual content, not just their URLs."],
                ["role": "user", "content": prompt]
            ],
            "max_tokens": 20
        ]

        do {
            let jsonData = try JSONSerialization.data(withJSONObject: parameters, options: [])
            request.httpBody = jsonData
        } catch {
            print("Error serializing JSON: \(error)")
            completion("web_link")
            return
        }

        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                print("Error making API request: \(error)")
                completion("web_link")
            } else if let data = data {
                do {
                    if let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
                       let choices = json["choices"] as? [[String: Any]],
                       let message = choices.first?["message"] as? [String: Any],
                       let text = message["content"] as? String {
                        let result = text.trimmingCharacters(in: .whitespacesAndNewlines)
                        completion(result)
                    } else {
                        completion("web_link")
                    }
                } catch {
                    print("Error parsing JSON response: \(error)")
                    completion("web_link")
                }
            }
        }
        task.resume()
    }

    // MARK: - Enhanced URL Analysis

    func categorizeMessageWithEnhancedURLAnalysis(_ message: String, availableCategories: [String], completion: @escaping (String) -> Void) {
        guard !availableCategories.isEmpty else {
            completion("Uncategorized")
            return
        }

        let urls = extractURLsFromMessage(message)

        if urls.isEmpty {
            // No URLs, use standard categorization
            let result = categorizeMessage(message, availableCategories: availableCategories)
            completion(result)
            return
        }

        // Analyze the first URL with full content analysis
        analyzeURLWithContent(urls[0]) { urlCategory in
            // Use the enhanced URL analysis for better categorization
            guard let apiKey = ProcessInfo.processInfo.environment["OPENAI_API_KEY"] else {
                completion("Uncategorized")
                return
            }

            let url = URL(string: "https://api.openai.com/v1/chat/completions")!
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.addValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
            request.addValue("application/json", forHTTPHeaderField: "Content-Type")

            let prompt = """
            Categorize the following message into one of these existing categories: \(availableCategories.joined(separator: ", ")).

            Message: \(message)
            URL Content Analysis: The URL in this message has been analyzed and appears to be related to: \(urlCategory)

            IMPORTANT RULES:
            1. STRONGLY prefer existing categories over creating new ones (90% of the time use existing)
            2. Use the URL content analysis to inform your decision
            3. Consider semantic similarity with existing categories
            4. Only create a NEW category if this represents a major life project/area that will generate many related messages

            If you must create a new category, make it:
            - Broad enough for future similar items
            - Clear and descriptive of the overall project/area
            - Properly capitalized (e.g., "Online Learning", "Digital Shopping")

            Return ONLY the category name, nothing else.
            """

            let parameters: [String: Any] = [
                "model": "gpt-4",
                "messages": [
                    ["role": "system", "content": "You are an intelligent categorization assistant that excels at understanding web content and organizing messages based on both text content and URL analysis."],
                    ["role": "user", "content": prompt]
                ],
                "max_tokens": 30
            ]

            do {
                let jsonData = try JSONSerialization.data(withJSONObject: parameters, options: [])
                request.httpBody = jsonData
            } catch {
                print("Error serializing JSON: \(error)")
                completion("Uncategorized")
                return
            }

            let task = URLSession.shared.dataTask(with: request) { data, response, error in
                if let error = error {
                    print("Error making API request: \(error)")
                    completion("Uncategorized")
                } else if let data = data {
                    do {
                        if let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
                           let choices = json["choices"] as? [[String: Any]],
                           let message = choices.first?["message"] as? [String: Any],
                           let text = message["content"] as? String {
                            let category = text.trimmingCharacters(in: .whitespacesAndNewlines)
                            completion(category)
                        } else {
                            completion("Uncategorized")
                        }
                    } catch {
                        print("Error parsing JSON response: \(error)")
                        completion("Uncategorized")
                    }
                }
            }
            task.resume()
        }
    }

    func getSFSymbolForCategory(_ category: String) -> String {
        // Use ChatGPT API for SFSymbol match
        guard let apiKey = ProcessInfo.processInfo.environment["OPENAI_API_KEY"] else {
            fatalError("API key not found in environment variables")
        }
        let url = URL(string: "https://api.openai.com/v1/chat/completions")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")

        let prompt = "What is the most apt SF Symbol that can be used in SwiftUI App to represent the category \"\(category)\"? Only return the SF Symbol string that can be used in SwiftUI App as the value for systemName. No other formatting and nothing else."
        let parameters: [String: Any] = [
            "model": "gpt-4",
            "messages": [
            ["role": "system", "content": "I am a helpful Swift coding assistant that finds the most beautiful and apt symbols to represent different categories in a SwiftUI app. I will take the best call even in times of ambiguity and return only one most fitting SF Symbol string that can be directly used in application code. Please provide me with a category name and I will use my advanced algorithms to find the most fitting SF Symbol for your messages."],
            ["role": "user", "content": prompt]
            ],
            "max_tokens": 20
        ]

        do {
            let jsonData = try JSONSerialization.data(withJSONObject: parameters, options: [])
            request.httpBody = jsonData
        } catch {
            print("Error serializing JSON: \(error)")
            return "Error"
        }

        let semaphore = DispatchSemaphore(value: 0)
        var symbol = "folder.fill" // Default category

        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
            print("Error making API request: \(error)")
            } else if let data = data {
            do {
                if let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
                   let choices = json["choices"] as? [[String: Any]],
                   let message = choices.first?["message"] as? [String: Any],
                   let text = message["content"] as? String {
                    symbol = text.trimmingCharacters(in: .whitespacesAndNewlines)
                    let quoteCharacterSet = CharacterSet(charactersIn: "\"")
                    symbol = symbol.trimmingCharacters(in: quoteCharacterSet)
                }
            } catch {
                print("Error parsing JSON response: \(error)")
            }
            }
            semaphore.signal()
        }

        task.resume()
        semaphore.wait()
        return symbol
    }

    // MARK: - Context-Aware Categorization

    func categorizeMessageWithContext(_ message: String, categoryManager: CategoryManager) -> (category: Category?, shouldCreateSubfolder: Bool, subfolderName: String?) {
        let availableCategories = categoryManager.getAllCategoryNames()
        let categoryName = categorizeMessage(message, availableCategories: availableCategories)

        // Find the target category
        guard let targetCategory = categoryManager.findCategory(named: categoryName) else {
            // Category doesn't exist, will be created as root category
            return (nil, false, nil)
        }

        // Check if we should create a subfolder
        let subfolderResult = shouldCreateSubfolder(for: message, in: targetCategory)

        if subfolderResult.shouldCreate, let subfolderName = subfolderResult.subfolderName {
            // Check if subfolder already exists
            if let existingSubfolder = targetCategory.subcategories.first(where: { $0.name == subfolderName }) {
                return (existingSubfolder, false, nil)
            } else {
                return (targetCategory, true, subfolderName)
            }
        }

        return (targetCategory, false, nil)
    }

    // MARK: - Smart Subfolder Logic

    func shouldCreateSubfolder(for message: String, in category: Category) -> (shouldCreate: Bool, subfolderName: String?) {
        let messageCount = category.getTotalMessageCountIncludingCompleted()

        // Don't create subfolders if the category doesn't have enough messages yet
        guard messageCount >= 5 else { return (false, nil) }

        // First check predefined patterns, then use AI for dynamic analysis
        if let predefinedResult = checkPredefinedSubfolderPatterns(message: message, category: category) {
            return predefinedResult
        }

        // Use AI to suggest dynamic subfolders for any category
        return suggestDynamicSubfolder(for: message, in: category)
    }

    private func checkPredefinedSubfolderPatterns(message: String, category: Category) -> (shouldCreate: Bool, subfolderName: String?)? {
        let categoryName = category.name.lowercased()
        let messageLower = message.lowercased()

        // Movies to Watch -> Genre-based subfolders
        if categoryName.contains("movie") || categoryName.contains("film") || categoryName.contains("watch") {
            if messageLower.contains("comedy") || messageLower.contains("funny") || messageLower.contains("laugh") {
                return (true, "Comedy")
            } else if messageLower.contains("action") || messageLower.contains("adventure") || messageLower.contains("thriller") {
                return (true, "Action & Adventure")
            } else if messageLower.contains("drama") || messageLower.contains("romantic") || messageLower.contains("romance") {
                return (true, "Drama & Romance")
            } else if messageLower.contains("horror") || messageLower.contains("scary") {
                return (true, "Horror & Thriller")
            } else if messageLower.contains("documentary") || messageLower.contains("docu") {
                return (true, "Documentaries")
            }
        }

        // Shopping -> Category-based subfolders
        if categoryName.contains("shop") {
            if messageLower.contains("cloth") || messageLower.contains("shirt") || messageLower.contains("dress") ||
               messageLower.contains("shoe") || messageLower.contains("fashion") {
                return (true, "Clothing & Fashion")
            } else if messageLower.contains("electronic") || messageLower.contains("phone") || messageLower.contains("computer") ||
                      messageLower.contains("gadget") || messageLower.contains("tech") {
                return (true, "Electronics")
            } else if messageLower.contains("book") || messageLower.contains("read") {
                return (true, "Books")
            } else if messageLower.contains("food") || messageLower.contains("grocery") || messageLower.contains("kitchen") {
                return (true, "Food & Grocery")
            } else if messageLower.contains("home") || messageLower.contains("furniture") || messageLower.contains("decor") {
                return (true, "Home & Garden")
            }
        }

        // To-Do -> Priority or type-based subfolders
        if categoryName.contains("to-do") || categoryName.contains("todo") || categoryName.contains("task") {
            if messageLower.contains("urgent") || messageLower.contains("asap") || messageLower.contains("important") {
                return (true, "Urgent")
            } else if messageLower.contains("work") || messageLower.contains("office") || messageLower.contains("meeting") {
                return (true, "Work")
            } else if messageLower.contains("personal") || messageLower.contains("home") || messageLower.contains("family") {
                return (true, "Personal")
            }
        }

        // To-Read -> Type-based subfolders
        if categoryName.contains("read") {
            if messageLower.contains("article") || messageLower.contains("blog") || messageLower.contains("news") {
                return (true, "Articles & Blogs")
            } else if messageLower.contains("book") || messageLower.contains("novel") {
                return (true, "Books")
            } else if messageLower.contains("research") || messageLower.contains("paper") || messageLower.contains("study") {
                return (true, "Research & Papers")
            }
        }

        return nil // No predefined pattern found
    }

    private func suggestDynamicSubfolder(for message: String, in category: Category) -> (shouldCreate: Bool, subfolderName: String?) {
        // Analyze existing messages in the category to understand patterns
        let existingMessages = category.messages.map { $0.text }
        let existingSubfolders = category.subcategories.map { $0.name }

        // Use AI to suggest a subfolder based on content analysis
        guard let apiKey = ProcessInfo.processInfo.environment["OPENAI_API_KEY"] else {
            return (false, nil)
        }

        let url = URL(string: "https://api.openai.com/v1/chat/completions")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")

        let existingMessagesText = existingMessages.prefix(10).joined(separator: "; ")
        let existingSubfoldersText = existingSubfolders.isEmpty ? "None" : existingSubfolders.joined(separator: ", ")

        let prompt = """
        Analyze if this new message should go into a subfolder within the "\(category.name)" category.

        Category: \(category.name)
        New Message: \(message)

        Existing messages in this category: \(existingMessagesText)
        Existing subfolders: \(existingSubfoldersText)

        RULES:
        1. Only suggest a subfolder if there's a clear, logical grouping that would help organize multiple similar items
        2. The subfolder name should be broad enough for future similar messages
        3. Consider natural groupings like: phases/stages, types/categories, priorities, time periods, locations, etc.
        4. If existing subfolders already cover this message, return the existing subfolder name
        5. Don't create subfolders for one-off items that won't have similar messages

        Examples of good subfolder patterns:
        - Book Writing: "Research", "Character Development", "Plot Outline", "Editing"
        - House Buying: "Research", "Viewing", "Financing", "Legal", "Moving"
        - Baby Planning: "Preparation", "Medical", "Shopping", "Nursery Setup"
        - Travel Planning: "Research", "Booking", "Packing", "Itinerary"
        - Project Management: "Planning", "Development", "Testing", "Documentation"

        Respond with either:
        - "NO_SUBFOLDER" if no subfolder is needed
        - Just the subfolder name if one should be created/used (e.g., "Research", "Planning Phase")

        Response:
        """

        let parameters: [String: Any] = [
            "model": "gpt-4",
            "messages": [
                ["role": "system", "content": "You are an expert at organizing information and creating logical folder structures. You understand how people naturally group related items and can identify when subcategorization would be helpful vs when it would be unnecessary complexity."],
                ["role": "user", "content": prompt]
            ],
            "max_tokens": 50
        ]

        do {
            let jsonData = try JSONSerialization.data(withJSONObject: parameters, options: [])
            request.httpBody = jsonData
        } catch {
            print("Error serializing JSON: \(error)")
            return (false, nil)
        }

        let semaphore = DispatchSemaphore(value: 0)
        var result: (shouldCreate: Bool, subfolderName: String?) = (false, nil)

        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                print("Error making API request: \(error)")
            } else if let data = data {
                do {
                    if let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
                       let choices = json["choices"] as? [[String: Any]],
                       let message = choices.first?["message"] as? [String: Any],
                       let text = message["content"] as? String {
                        let response = text.trimmingCharacters(in: .whitespacesAndNewlines)

                        if response != "NO_SUBFOLDER" && !response.isEmpty {
                            result = (true, response)
                        }
                    }
                } catch {
                    print("Error parsing JSON response: \(error)")
                }
            }
            semaphore.signal()
        }

        task.resume()
        semaphore.wait()
        return result
    }

    // MARK: - Category Analysis & Optimization

    func analyzeCategoryForOptimization(_ category: Category) -> [String] {
        guard category.messages.count >= 8 else { return [] }

        let messages = category.messages.map { $0.text }
        let messageText = messages.joined(separator: "; ")

        guard let apiKey = ProcessInfo.processInfo.environment["OPENAI_API_KEY"] else {
            return []
        }

        let url = URL(string: "https://api.openai.com/v1/chat/completions")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")

        let prompt = """
        Analyze these messages in the "\(category.name)" category and suggest logical subfolders to organize them better.

        Messages: \(messageText)

        Look for natural patterns like:
        - Phases/stages of a process
        - Different types or categories of items
        - Priority levels or urgency
        - Time periods or deadlines
        - Locations or contexts
        - Status or completion stages

        Suggest 2-5 subfolder names that would help organize these messages logically. Each subfolder should be:
        - Broad enough to contain multiple messages
        - Clear and descriptive
        - Useful for future similar messages

        Return only the subfolder names, one per line, or "NO_SUGGESTIONS" if the messages don't show clear patterns.
        """

        let parameters: [String: Any] = [
            "model": "gpt-4",
            "messages": [
                ["role": "system", "content": "You are an expert at analyzing content patterns and suggesting logical organizational structures. You help people create folder systems that make sense and will be useful long-term."],
                ["role": "user", "content": prompt]
            ],
            "max_tokens": 100
        ]

        do {
            let jsonData = try JSONSerialization.data(withJSONObject: parameters, options: [])
            request.httpBody = jsonData
        } catch {
            print("Error serializing JSON: \(error)")
            return []
        }

        let semaphore = DispatchSemaphore(value: 0)
        var suggestions: [String] = []

        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                print("Error making API request: \(error)")
            } else if let data = data {
                do {
                    if let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
                       let choices = json["choices"] as? [[String: Any]],
                       let message = choices.first?["message"] as? [String: Any],
                       let text = message["content"] as? String {
                        let response = text.trimmingCharacters(in: .whitespacesAndNewlines)

                        if response != "NO_SUGGESTIONS" {
                            suggestions = response.components(separatedBy: .newlines)
                                .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
                                .filter { !$0.isEmpty }
                        }
                    }
                } catch {
                    print("Error parsing JSON response: \(error)")
                }
            }
            semaphore.signal()
        }

        task.resume()
        semaphore.wait()
        return suggestions
    }

    func categorizeMessage(_ message: String, availableCategories: [String]) -> String {
        guard !availableCategories.isEmpty else { return "Uncategorized" }

        // For messages with URLs, we'll do a quick domain-based analysis first
        // The full content analysis can be done asynchronously later if needed
        let urls = extractURLsFromMessage(message)
        let urlContext = urls.map { analyzeURLContent($0) }.joined(separator: ", ")

        // Use ChatGPT API for categorization
        guard let apiKey = ProcessInfo.processInfo.environment["OPENAI_API_KEY"] else {
            fatalError("API key not found in environment variables")
        }
        let url = URL(string: "https://api.openai.com/v1/chat/completions")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")

        let urlAnalysis = urls.isEmpty ? "" : "\n\nURL Analysis: The message contains URLs that appear to be related to: \(urlContext)"

        let prompt = """
        Categorize the following message into one of these existing categories: \(availableCategories.joined(separator: ", ")).

        IMPORTANT RULES:
        1. STRONGLY prefer existing categories over creating new ones (90% of the time use existing)
        2. For movies/shows: Consider if they fit into existing entertainment categories
        3. For shopping items: Use "Shopping" unless there's a more specific existing category
        4. For URLs: Consider the website content and purpose, not just the URL itself
        5. For tasks/reminders: Use "To-Do" unless there's a more specific category
        6. For books/articles: Use "To-Read" unless there's a more specific category
        7. Look for semantic similarity - "Book Writing" could fit in "Writing", "House Hunting" could fit in "Real Estate" or "Home"
        8. Consider broader themes - baby planning could fit in "Family", travel planning in "Travel", etc.

        Only create a NEW category if:
        - The message represents a major life project/area (like "Wedding Planning", "Career Change", "Health Journey")
        - It's a significant ongoing endeavor that will generate many related messages
        - It truly doesn't fit semantically into any existing category

        If you must create a new category, make it:
        - Broad enough for future similar items (think "Book Writing" not "Chapter 3 Notes")
        - Clear and descriptive of the overall project/area
        - Properly capitalized (e.g., "House Buying", "Baby Planning", "Business Startup")
        - Future-oriented (will this category be useful for 10+ related messages?)

        Examples of good new categories for major life projects:
        - "Wedding Planning" (for engagement, venue, catering, etc.)
        - "House Buying" (for research, viewing, financing, moving)
        - "Baby Planning" (for preparation, medical, shopping, setup)
        - "Book Writing" (for research, writing, editing, publishing)
        - "Career Change" (for job search, networking, skill building)
        - "Health Journey" (for fitness, nutrition, medical, wellness)
        - "Business Startup" (for planning, legal, marketing, development)

        Return ONLY the category name, nothing else.\(urlAnalysis)

        Message: \(message)
        """

        let parameters: [String: Any] = [
            "model": "gpt-4",
            "messages": [
                ["role": "system", "content": "You are an intelligent categorization assistant for a personal organization app. Your goal is to help users organize their messages, links, tasks, and notes into meaningful categories. You excel at understanding context, recognizing patterns, and making smart decisions about where content belongs. You strongly prefer using existing categories and only create new ones when absolutely necessary. You understand that good organization means having the right balance - not too many categories (overwhelming) but enough to be useful."],
                ["role": "user", "content": prompt]
            ],
            "max_tokens": 30
        ]
        
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: parameters, options: [])
            request.httpBody = jsonData
        } catch {
            print("Error serializing JSON: \(error)")
            return "Error"
        }
        
        let semaphore = DispatchSemaphore(value: 0)
        var category = "To-Do" // Default category
        
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
            print("Error making API request: \(error)")
            } else if let data = data {
            do {
                if let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
                   let choices = json["choices"] as? [[String: Any]],
                   let message = choices.first?["message"] as? [String: Any],
                   let text = message["content"] as? String {
                    category = text.trimmingCharacters(in: .whitespacesAndNewlines)
                    let quoteCharacterSet = CharacterSet(charactersIn: "\"")
                    category = category.trimmingCharacters(in: quoteCharacterSet)
                }
            } catch {
                print("Error parsing JSON response: \(error)")
            }
            }
            semaphore.signal()
        }
        
        task.resume()
        semaphore.wait()
        return category
    }
}
