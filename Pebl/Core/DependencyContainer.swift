//
//  DependencyContainer.swift
//  Pebl
//
//  Created by AI Assistant on 6/17/25.
//

import Foundation

// MARK: - Dependency Container Protocol

protocol DependencyContainer {
    func register<T>(_ type: T.Type, factory: @escaping () -> T)
    func register<T>(_ type: T.Type, instance: T)
    func resolve<T>(_ type: T.Type) -> T
    func resolve<T>(_ type: T.Type) -> T?
}

// MARK: - Simple Dependency Container Implementation

class SimpleDependencyContainer: DependencyContainer {
    private var factories: [String: () -> Any] = [:]
    private var singletons: [String: Any] = [:]
    
    func register<T>(_ type: T.Type, factory: @escaping () -> T) {
        let key = String(describing: type)
        factories[key] = factory
    }
    
    func register<T>(_ type: T.Type, instance: T) {
        let key = String(describing: type)
        singletons[key] = instance
    }
    
    func resolve<T>(_ type: T.Type) -> T {
        guard let instance: T = resolve(type) else {
            fatalError("No registration found for type \(type)")
        }
        return instance
    }
    
    func resolve<T>(_ type: T.Type) -> T? {
        let key = String(describing: type)
        
        // Check singletons first
        if let singleton = singletons[key] as? T {
            return singleton
        }
        
        // Check factories
        if let factory = factories[key] {
            return factory() as? T
        }
        
        return nil
    }
}

// MARK: - Service Factory

class CategorizationServiceFactoryImpl: CategorizationServiceFactory {
    private let container: DependencyContainer
    
    init(container: DependencyContainer) {
        self.container = container
        setupDependencies()
    }
    
    private func setupDependencies() {
        // Register configuration
        container.register(ConfigurationProvider.self) {
            ConfigurationManager.shared
        }
        
        // Register logger
        container.register(Logger.self) {
            ConsoleLogger()
        }
        
        // Register cache
        container.register(AnalysisCache.self) {
            let logger = self.container.resolve(Logger.self)
            return MemoryAnalysisCache(logger: logger)
        }
        
        // Register AI service
        container.register(AIServiceProtocol.self) {
            let config = self.container.resolve(ConfigurationProvider.self)
            let logger = self.container.resolve(Logger.self)
            return OpenAIService(config: config, logger: logger)
        }
        
        // Register error handler
        container.register(ErrorHandler.self) {
            let logger = self.container.resolve(Logger.self)
            return ErrorHandler(logger: logger)
        }
        
        // Register retry handler
        container.register(RetryHandler.self) {
            let config = self.container.resolve(ConfigurationProvider.self)
            let aiSettings = config.getAISettings()
            return RetryHandler(maxAttempts: aiSettings.retryAttempts)
        }
        
        // Register circuit breaker
        container.register(CircuitBreaker.self) {
            CircuitBreaker()
        }
    }
    
    func createURLAnalyzer() -> URLAnalyzer {
        let aiService = container.resolve(AIServiceProtocol.self)
        let cache = container.resolve(AnalysisCache.self)
        let config = container.resolve(ConfigurationProvider.self)
        let logger = container.resolve(Logger.self)
        
        let primaryAnalyzer = AIURLAnalyzer(
            aiService: aiService,
            cache: cache,
            config: config,
            logger: logger
        )
        
        let fallbackAnalyzer = FallbackURLAnalyzer(logger: logger)
        
        return CompositeURLAnalyzer(
            primaryAnalyzer: primaryAnalyzer,
            fallbackAnalyzer: fallbackAnalyzer,
            logger: logger
        )
    }
    
    func createMessageCategorizer() -> MessageCategorizer {
        let aiService = container.resolve(AIServiceProtocol.self)
        let cache = container.resolve(AnalysisCache.self)
        let config = container.resolve(ConfigurationProvider.self)
        let logger = container.resolve(Logger.self)
        let urlAnalyzer = createURLAnalyzer()
        
        return AIMessageCategorizer(
            aiService: aiService,
            urlAnalyzer: urlAnalyzer,
            cache: cache,
            config: config,
            logger: logger
        )
    }
    
    func createSubfolderAnalyzer() -> SubfolderAnalyzer {
        let config = container.resolve(ConfigurationProvider.self)
        let logger = container.resolve(Logger.self)
        
        let patternAnalyzer = PatternBasedSubfolderAnalyzer(
            config: config,
            logger: logger
        )
        
        let aiService = container.resolve(AIServiceProtocol.self)
        let cache = container.resolve(AnalysisCache.self)
        
        let aiAnalyzer = AISubfolderAnalyzer(
            aiService: aiService,
            cache: cache,
            config: config,
            logger: logger
        )
        
        return CompositeSubfolderAnalyzer(
            patternAnalyzer: patternAnalyzer,
            aiAnalyzer: aiAnalyzer,
            logger: logger
        )
    }
    
    func createSymbolGenerator() -> SymbolGenerator {
        let aiService = container.resolve(AIServiceProtocol.self)
        let cache = container.resolve(AnalysisCache.self)
        let config = container.resolve(ConfigurationProvider.self)
        let logger = container.resolve(Logger.self)
        
        return AISymbolGenerator(
            aiService: aiService,
            cache: cache,
            config: config,
            logger: logger
        )
    }
    
    func createCache() -> AnalysisCache {
        return container.resolve(AnalysisCache.self)
    }
    
    func createAIService() -> AIServiceProtocol {
        return container.resolve(AIServiceProtocol.self)
    }
}

// MARK: - Service Locator (Alternative to DI)

class ServiceLocator {
    static let shared = ServiceLocator()
    private let container = SimpleDependencyContainer()
    private let factory: CategorizationServiceFactory
    
    private init() {
        self.factory = CategorizationServiceFactoryImpl(container: container)
    }
    
    func getURLAnalyzer() -> URLAnalyzer {
        return factory.createURLAnalyzer()
    }
    
    func getMessageCategorizer() -> MessageCategorizer {
        return factory.createMessageCategorizer()
    }
    
    func getSubfolderAnalyzer() -> SubfolderAnalyzer {
        return factory.createSubfolderAnalyzer()
    }
    
    func getSymbolGenerator() -> SymbolGenerator {
        return factory.createSymbolGenerator()
    }
    
    func getCache() -> AnalysisCache {
        return factory.createCache()
    }
    
    func getErrorHandler() -> ErrorHandler {
        return container.resolve(ErrorHandler.self)
    }
    
    func getRetryHandler() -> RetryHandler {
        return container.resolve(RetryHandler.self)
    }
    
    func getCircuitBreaker() -> CircuitBreaker {
        return container.resolve(CircuitBreaker.self)
    }
    
    func getLogger() -> Logger {
        return container.resolve(Logger.self)
    }
}

// MARK: - Dependency Injection for Testing

class TestDependencyContainer: SimpleDependencyContainer {
    func registerMock<T>(_ type: T.Type, mock: T) {
        register(type, instance: mock)
    }
    
    func createTestFactory() -> CategorizationServiceFactory {
        return CategorizationServiceFactoryImpl(container: self)
    }
}

// MARK: - Configuration for Different Environments

enum Environment {
    case development
    case testing
    case production
}

class EnvironmentConfiguration {
    static func setupContainer(for environment: Environment) -> DependencyContainer {
        let container = SimpleDependencyContainer()
        
        switch environment {
        case .development:
            setupDevelopmentDependencies(container)
        case .testing:
            setupTestingDependencies(container)
        case .production:
            setupProductionDependencies(container)
        }
        
        return container
    }
    
    private static func setupDevelopmentDependencies(_ container: DependencyContainer) {
        container.register(Logger.self) {
            ConsoleLogger() // Verbose logging for development
        }
        
        container.register(AnalysisCache.self) {
            MemoryAnalysisCache(maxSize: 100) // Smaller cache for development
        }
    }
    
    private static func setupTestingDependencies(_ container: DependencyContainer) {
        container.register(Logger.self) {
            MockLogger() // No-op logger for tests
        }
        
        container.register(AnalysisCache.self) {
            MockCache() // Mock cache for tests
        }
    }
    
    private static func setupProductionDependencies(_ container: DependencyContainer) {
        container.register(Logger.self) {
            ConsoleLogger() // Production logging
        }
        
        container.register(AnalysisCache.self) {
            do {
                return try PersistentAnalysisCache() // Persistent cache for production
            } catch {
                return MemoryAnalysisCache() // Fallback to memory cache
            }
        }
    }
}

// MARK: - Mock Implementations for Testing

class MockLogger: Logger {
    func debug(_ message: String) {}
    func info(_ message: String) {}
    func warning(_ message: String) {}
    func error(_ message: String) {}
}

class MockCache: AnalysisCache {
    func getCachedResult<T: Codable>(for key: String, type: T.Type) -> T? { return nil }
    func setCachedResult<T: Codable>(_ result: T, for key: String, ttl: TimeInterval) {}
    func clearCache() {}
    func clearExpiredEntries() {}
}
