//
//  CachingService.swift
//  Pebl
//
//  Created by AI Assistant on 6/17/25.
//

import Foundation

// MARK: - Cache Entry

private struct CacheEntry<T: Codable>: Codable {
    let value: T
    let timestamp: Date
    let ttl: TimeInterval
    
    var isExpired: Bool {
        Date().timeIntervalSince(timestamp) > ttl
    }
}

// MARK: - Memory Cache Implementation

class MemoryAnalysisCache: AnalysisCache {
    private var cache: [String: Any] = [:]
    private let queue = DispatchQueue(label: "com.pebl.cache", attributes: .concurrent)
    private let maxSize: Int
    private let logger: Logger
    
    init(maxSize: Int = 1000, logger: Logger = ConsoleLogger()) {
        self.maxSize = maxSize
        self.logger = logger
    }
    
    func getCachedResult<T: Codable>(for key: String, type: T.Type) -> T? {
        return queue.sync {
            guard let entry = cache[key] as? CacheEntry<T> else {
                return nil
            }
            
            if entry.isExpired {
                cache.removeValue(forKey: key)
                logger.debug("Cache entry expired for key: \(key)")
                return nil
            }
            
            logger.debug("Cache hit for key: \(key)")
            return entry.value
        }
    }
    
    func setCachedResult<T: Codable>(_ result: T, for key: String, ttl: TimeInterval) {
        queue.async(flags: .barrier) {
            // Ensure cache doesn't exceed max size
            if self.cache.count >= self.maxSize {
                self.evictOldestEntries()
            }
            
            let entry = CacheEntry(value: result, timestamp: Date(), ttl: ttl)
            self.cache[key] = entry
            self.logger.debug("Cached result for key: \(key)")
        }
    }
    
    func clearCache() {
        queue.async(flags: .barrier) {
            self.cache.removeAll()
            self.logger.info("Cache cleared")
        }
    }
    
    func clearExpiredEntries() {
        queue.async(flags: .barrier) {
            let initialCount = self.cache.count
            
            self.cache = self.cache.compactMapValues { value in
                guard let entry = value as? CacheEntry<Any> else { return value }
                return entry.isExpired ? nil : value
            }
            
            let removedCount = initialCount - self.cache.count
            if removedCount > 0 {
                self.logger.info("Removed \(removedCount) expired cache entries")
            }
        }
    }
    
    private func evictOldestEntries() {
        // Remove 20% of entries to make room
        let entriesToRemove = max(1, maxSize / 5)
        
        // Sort by timestamp and remove oldest
        let sortedKeys = cache.keys.sorted { key1, key2 in
            guard let entry1 = cache[key1] as? CacheEntry<Any>,
                  let entry2 = cache[key2] as? CacheEntry<Any> else {
                return false
            }
            return entry1.timestamp < entry2.timestamp
        }
        
        for key in sortedKeys.prefix(entriesToRemove) {
            cache.removeValue(forKey: key)
        }
        
        logger.debug("Evicted \(entriesToRemove) cache entries")
    }
}

// MARK: - Persistent Cache Implementation

class PersistentAnalysisCache: AnalysisCache {
    private let memoryCache: MemoryAnalysisCache
    private let fileManager = FileManager.default
    private let cacheDirectory: URL
    private let logger: Logger
    
    init(maxMemorySize: Int = 500, logger: Logger = ConsoleLogger()) throws {
        self.memoryCache = MemoryAnalysisCache(maxSize: maxMemorySize, logger: logger)
        self.logger = logger
        
        // Create cache directory
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        self.cacheDirectory = documentsPath.appendingPathComponent("CategorizationCache")
        
        try fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)
    }
    
    func getCachedResult<T: Codable>(for key: String, type: T.Type) -> T? {
        // Try memory cache first
        if let result = memoryCache.getCachedResult(for: key, type: type) {
            return result
        }
        
        // Try disk cache
        return getDiskCachedResult(for: key, type: type)
    }
    
    func setCachedResult<T: Codable>(_ result: T, for key: String, ttl: TimeInterval) {
        // Store in memory cache
        memoryCache.setCachedResult(result, for: key, ttl: ttl)
        
        // Store in disk cache for persistence
        setDiskCachedResult(result, for: key, ttl: ttl)
    }
    
    func clearCache() {
        memoryCache.clearCache()
        clearDiskCache()
    }
    
    func clearExpiredEntries() {
        memoryCache.clearExpiredEntries()
        clearExpiredDiskEntries()
    }
    
    private func getDiskCachedResult<T: Codable>(for key: String, type: T.Type) -> T? {
        let fileURL = cacheDirectory.appendingPathComponent("\(key.hash).cache")
        
        guard fileManager.fileExists(atPath: fileURL.path),
              let data = try? Data(contentsOf: fileURL),
              let entry = try? JSONDecoder().decode(CacheEntry<T>.self, from: data) else {
            return nil
        }
        
        if entry.isExpired {
            try? fileManager.removeItem(at: fileURL)
            logger.debug("Disk cache entry expired for key: \(key)")
            return nil
        }
        
        // Store in memory cache for faster access
        memoryCache.setCachedResult(entry.value, for: key, ttl: entry.ttl)
        
        logger.debug("Disk cache hit for key: \(key)")
        return entry.value
    }
    
    private func setDiskCachedResult<T: Codable>(_ result: T, for key: String, ttl: TimeInterval) {
        let entry = CacheEntry(value: result, timestamp: Date(), ttl: ttl)
        let fileURL = cacheDirectory.appendingPathComponent("\(key.hash).cache")
        
        do {
            let data = try JSONEncoder().encode(entry)
            try data.write(to: fileURL)
            logger.debug("Stored disk cache for key: \(key)")
        } catch {
            logger.error("Failed to store disk cache for key \(key): \(error)")
        }
    }
    
    private func clearDiskCache() {
        do {
            let files = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: nil)
            for file in files {
                try fileManager.removeItem(at: file)
            }
            logger.info("Disk cache cleared")
        } catch {
            logger.error("Failed to clear disk cache: \(error)")
        }
    }
    
    private func clearExpiredDiskEntries() {
        do {
            let files = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: nil)
            var removedCount = 0
            
            for file in files {
                if let data = try? Data(contentsOf: file),
                   let entry = try? JSONDecoder().decode(CacheEntry<AnyCodable>.self, from: data),
                   entry.isExpired {
                    try fileManager.removeItem(at: file)
                    removedCount += 1
                }
            }
            
            if removedCount > 0 {
                logger.info("Removed \(removedCount) expired disk cache entries")
            }
        } catch {
            logger.error("Failed to clear expired disk cache entries: \(error)")
        }
    }
}

// MARK: - Cache Key Generator

struct CacheKeyGenerator {
    static func urlAnalysisKey(for url: String) -> String {
        return "url_analysis_\(url.hash)"
    }
    
    static func categorizationKey(for message: String, categories: [String]) -> String {
        let categoriesHash = categories.joined(separator: ",").hash
        return "categorization_\(message.hash)_\(categoriesHash)"
    }
    
    static func subfolderKey(for message: String, category: String) -> String {
        return "subfolder_\(message.hash)_\(category.hash)"
    }
    
    static func sfSymbolKey(for category: String) -> String {
        return "sf_symbol_\(category.hash)"
    }
}

// MARK: - Helper Type for Any Codable

private struct AnyCodable: Codable {
    let value: Any
    
    init<T: Codable>(_ value: T) {
        self.value = value
    }
    
    init(from decoder: Decoder) throws {
        // This is a simplified implementation
        // In practice, you'd need more sophisticated type handling
        let container = try decoder.singleValueContainer()
        if let string = try? container.decode(String.self) {
            value = string
        } else if let int = try? container.decode(Int.self) {
            value = int
        } else if let double = try? container.decode(Double.self) {
            value = double
        } else if let bool = try? container.decode(Bool.self) {
            value = bool
        } else {
            throw DecodingError.typeMismatch(AnyCodable.self, DecodingError.Context(codingPath: decoder.codingPath, debugDescription: "Unsupported type"))
        }
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        if let string = value as? String {
            try container.encode(string)
        } else if let int = value as? Int {
            try container.encode(int)
        } else if let double = value as? Double {
            try container.encode(double)
        } else if let bool = value as? Bool {
            try container.encode(bool)
        } else {
            throw EncodingError.invalidValue(value, EncodingError.Context(codingPath: encoder.codingPath, debugDescription: "Unsupported type"))
        }
    }
}
