//
//  RefactoredAIModel.swift
//  Pebl
//
//  Created by AI Assistant on 6/17/25.
//

import Foundation

// MARK: - Modern AI Model Implementation

class RefactoredAIModel {
    private let messageCategorizer: MessageCategorizer
    private let subfolderAnalyzer: SubfolderAnalyzer
    private let symbolGenerator: SymbolGenerator
    private let urlAnalyzer: URLAnalyzer
    private let errorHandler: <PERSON>rrorHandler
    private let retryHandler: RetryHandler
    private let circuitBreaker: CircuitBreaker
    private let logger: Logger
    
    // MARK: - Initialization
    
    init(factory: CategorizationServiceFactory = ServiceLocator.shared) {
        self.messageCategorizer = factory.createMessageCategorizer()
        self.subfolderAnalyzer = factory.createSubfolderAnalyzer()
        self.symbolGenerator = factory.createSymbolGenerator()
        self.urlAnalyzer = factory.createURLAnalyzer()
        self.errorHandler = ServiceLocator.shared.getErrorHandler()
        self.retryHandler = ServiceLocator.shared.getRetryHandler()
        self.circuitBreaker = ServiceLocator.shared.getCircuitBreaker()
        self.logger = ServiceLocator.shared.getLogger()
    }
    
    // MARK: - Public API (Backward Compatibility)
    
    /// Legacy method for backward compatibility
    func categorizeMessage(_ message: String, availableCategories: [String]) -> String {
        return performSyncOperation {
            try await self.messageCategorizer.categorizeMessage(message, availableCategories: availableCategories)
        }
    }
    
    /// Legacy method for backward compatibility
    func getSFSymbolForCategory(_ category: String) -> String {
        return performSyncOperation {
            try await self.symbolGenerator.getSFSymbol(for: category)
        }
    }
    
    /// Legacy method for backward compatibility
    func shouldCreateSubfolder(for message: String, in category: Category) -> (shouldCreate: Bool, subfolderName: String?) {
        let suggestion = performSyncOperation {
            try await self.subfolderAnalyzer.shouldCreateSubfolder(for: message, in: category)
        }
        return (suggestion.shouldCreate, suggestion.subfolderName)
    }
    
    /// Legacy method for backward compatibility
    func categorizeMessageWithContext(_ message: String, categoryManager: CategoryManager) -> (category: Category?, shouldCreateSubfolder: Bool, subfolderName: String?) {
        let result = performSyncOperation {
            try await self.messageCategorizer.categorizeMessageWithContext(message, categoryManager: categoryManager)
        }
        return (result.category, result.shouldCreateSubfolder, result.subfolderName)
    }
    
    /// Legacy method for backward compatibility
    func analyzeCategoryForOptimization(_ category: Category) -> [String] {
        return performSyncOperation {
            try await self.subfolderAnalyzer.analyzeExistingCategory(category)
        }
    }
    
    /// Legacy method for backward compatibility
    func analyzeURLContent(_ url: String) -> String {
        let result = performSyncOperation {
            try await self.urlAnalyzer.analyzeURL(url)
        }
        return result.category
    }
    
    /// Legacy method for backward compatibility
    func extractURLsFromMessage(_ message: String) -> [String] {
        guard let detector = try? NSDataDetector(types: NSTextCheckingResult.CheckingType.link.rawValue) else {
            return []
        }
        
        let matches = detector.matches(in: message, options: [], range: NSRange(location: 0, length: message.utf16.count))
        
        return matches.compactMap { match in
            if let range = Range(match.range, in: message) {
                return String(message[range])
            }
            return nil
        }
    }
    
    // MARK: - Modern Async API
    
    func categorizeMessageAsync(_ message: String, availableCategories: [String]) async throws -> String {
        return try await withErrorHandling(context: "categorizeMessage") {
            try await self.messageCategorizer.categorizeMessage(message, availableCategories: availableCategories)
        }
    }
    
    func getSFSymbolAsync(for category: String) async throws -> String {
        return try await withErrorHandling(context: "getSFSymbol") {
            try await self.symbolGenerator.getSFSymbol(for: category)
        }
    }
    
    func shouldCreateSubfolderAsync(for message: String, in category: Category) async throws -> SubfolderSuggestion {
        return try await withErrorHandling(context: "shouldCreateSubfolder") {
            try await self.subfolderAnalyzer.shouldCreateSubfolder(for: message, in: category)
        }
    }
    
    func categorizeMessageWithContextAsync(_ message: String, categoryManager: CategoryManagerProtocol) async throws -> CategorizationResult {
        return try await withErrorHandling(context: "categorizeMessageWithContext") {
            try await self.messageCategorizer.categorizeMessageWithContext(message, categoryManager: categoryManager)
        }
    }
    
    func analyzeCategoryForOptimizationAsync(_ category: Category) async throws -> [String] {
        return try await withErrorHandling(context: "analyzeCategoryForOptimization") {
            try await self.subfolderAnalyzer.analyzeExistingCategory(category)
        }
    }
    
    func analyzeURLAsync(_ url: String) async throws -> URLAnalysisResult {
        return try await withErrorHandling(context: "analyzeURL") {
            try await self.urlAnalyzer.analyzeURL(url)
        }
    }
    
    func analyzeURLWithContentAsync(_ url: String) async throws -> URLAnalysisResult {
        return try await withErrorHandling(context: "analyzeURLWithContent") {
            try await self.urlAnalyzer.analyzeURLWithContent(url)
        }
    }
    
    // MARK: - Enhanced Categorization with Full Context
    
    func categorizeMessageWithEnhancedURLAnalysis(_ message: String, availableCategories: [String]) async throws -> String {
        return try await withErrorHandling(context: "categorizeMessageWithEnhancedURLAnalysis") {
            let urls = self.extractURLsFromMessage(message)
            
            if urls.isEmpty {
                return try await self.messageCategorizer.categorizeMessage(message, availableCategories: availableCategories)
            }
            
            // Analyze the first URL with full content analysis
            let urlAnalysis = try await self.urlAnalyzer.analyzeURLWithContent(urls[0])
            
            // Use enhanced categorization with URL context
            return try await self.performEnhancedCategorization(
                message: message,
                availableCategories: availableCategories,
                urlContext: urlAnalysis.category
            )
        }
    }
    
    // MARK: - Private Helper Methods
    
    private func performSyncOperation<T>(_ operation: @escaping () async throws -> T) -> T {
        let semaphore = DispatchSemaphore(value: 0)
        var result: Result<T, Error>!
        
        Task {
            do {
                let value = try await operation()
                result = .success(value)
            } catch {
                result = .failure(error)
            }
            semaphore.signal()
        }
        
        semaphore.wait()
        
        do {
            return try result.get()
        } catch {
            errorHandler.handle(error as? CategorizationError ?? CategorizationError.invalidResponse, context: "syncOperation")
            
            // Return sensible defaults for backward compatibility
            if T.self == String.self {
                return "Uncategorized" as! T
            } else if T.self == [String].self {
                return [] as! T
            } else if T.self == (Bool, String?).self {
                return (false, nil) as! T
            } else {
                fatalError("Unsupported return type for sync operation")
            }
        }
    }
    
    private func withErrorHandling<T>(context: String, operation: @escaping () async throws -> T) async throws -> T {
        return try await circuitBreaker.execute {
            try await self.retryHandler.execute {
                try await operation()
            }
        }
    }
    
    private func performEnhancedCategorization(message: String, availableCategories: [String], urlContext: String) async throws -> String {
        let config = ConfigurationManager.shared
        let aiSettings = config.getAISettings()
        let aiService = ServiceLocator.shared.createAIService()
        
        let prompt = """
        Categorize the following message into one of these existing categories: \(availableCategories.joined(separator: ", ")).
        
        Message: \(message)
        URL Content Analysis: The URL in this message has been analyzed and appears to be related to: \(urlContext)
        
        IMPORTANT RULES:
        1. STRONGLY prefer existing categories over creating new ones (90% of the time use existing)
        2. Use the URL content analysis to inform your decision
        3. Consider semantic similarity with existing categories
        4. Only create a NEW category if this represents a major life project/area that will generate many related messages
        
        If you must create a new category, make it:
        - Broad enough for future similar items
        - Clear and descriptive of the overall project/area
        - Properly capitalized (e.g., "Online Learning", "Digital Shopping")
        
        Return ONLY the category name, nothing else.
        """
        
        return try await aiService.makeRequest(
            prompt: prompt,
            maxTokens: aiSettings.maxTokens.categorization,
            timeout: aiSettings.timeouts.aiRequest
        )
    }
}

// MARK: - Category Manager Protocol Conformance

extension CategoryManager: CategoryManagerProtocol {
    // Already conforms to the protocol
}

// MARK: - Migration Helper

class AIModelMigrationHelper {
    static func migrateToRefactoredModel() -> RefactoredAIModel {
        // Validate configuration
        let config = ConfigurationManager.shared
        let validationErrors = config.validateConfiguration()
        
        if !validationErrors.isEmpty {
            fatalError("Configuration validation failed: \(validationErrors.joined(separator: ", "))")
        }
        
        // Create and return the new model
        return RefactoredAIModel()
    }
    
    static func performHealthCheck() -> Bool {
        do {
            let model = RefactoredAIModel()
            
            // Test basic functionality
            Task {
                _ = try await model.categorizeMessageAsync("Test message", availableCategories: ["Test"])
            }
            
            return true
        } catch {
            print("Health check failed: \(error)")
            return false
        }
    }
}
