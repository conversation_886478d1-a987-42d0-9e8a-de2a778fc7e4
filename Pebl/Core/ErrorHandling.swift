//
//  ErrorHandling.swift
//  Pebl
//
//  Created by AI Assistant on 6/17/25.
//

import Foundation

// MARK: - Error Handler

class ErrorHandler {
    static let shared = ErrorHandler()
    
    private let analytics: CategorizationAnalytics?
    private let logger: Logger
    
    init(analytics: CategorizationAnalytics? = nil, logger: Logger = ConsoleLogger()) {
        self.analytics = analytics
        self.logger = logger
    }
    
    func handle(_ error: CategorizationError, context: String = "") {
        // Log the error
        logger.error("[\(context)] \(error.localizedDescription)")
        
        // Track analytics
        analytics?.trackError(error)
        
        // Handle specific error types
        switch error {
        case .rateLimitExceeded:
            handleRateLimitError()
        case .networkError:
            handleNetworkError()
        case .timeout:
            handleTimeoutError()
        default:
            break
        }
    }
    
    private func handleRateLimitError() {
        // Implement exponential backoff or rate limiting logic
        logger.warning("Rate limit exceeded. Consider implementing backoff strategy.")
    }
    
    private func handleNetworkError() {
        // Implement network retry logic
        logger.warning("Network error occurred. Consider retry with exponential backoff.")
    }
    
    private func handleTimeoutError() {
        // Handle timeout scenarios
        logger.warning("Request timed out. Consider increasing timeout or using fallback.")
    }
}

// MARK: - Retry Logic

class RetryHandler {
    private let maxAttempts: Int
    private let baseDelay: TimeInterval
    private let maxDelay: TimeInterval
    
    init(maxAttempts: Int = 3, baseDelay: TimeInterval = 1.0, maxDelay: TimeInterval = 30.0) {
        self.maxAttempts = maxAttempts
        self.baseDelay = baseDelay
        self.maxDelay = maxDelay
    }
    
    func execute<T>(_ operation: @escaping () async throws -> T) async throws -> T {
        var lastError: Error?
        
        for attempt in 1...maxAttempts {
            do {
                return try await operation()
            } catch {
                lastError = error
                
                // Don't retry on certain errors
                if let categorizationError = error as? CategorizationError {
                    switch categorizationError {
                    case .apiKeyMissing, .invalidConfiguration, .configurationMissing:
                        throw error // Don't retry these
                    default:
                        break
                    }
                }
                
                // Calculate delay with exponential backoff
                if attempt < maxAttempts {
                    let delay = min(baseDelay * pow(2.0, Double(attempt - 1)), maxDelay)
                    try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
                }
            }
        }
        
        throw lastError ?? CategorizationError.invalidResponse
    }
}

// MARK: - Circuit Breaker

class CircuitBreaker {
    enum State {
        case closed
        case open
        case halfOpen
    }
    
    private var state: State = .closed
    private var failureCount = 0
    private var lastFailureTime: Date?
    private let failureThreshold: Int
    private let recoveryTimeout: TimeInterval
    
    init(failureThreshold: Int = 5, recoveryTimeout: TimeInterval = 60.0) {
        self.failureThreshold = failureThreshold
        self.recoveryTimeout = recoveryTimeout
    }
    
    func execute<T>(_ operation: @escaping () async throws -> T) async throws -> T {
        switch state {
        case .open:
            if shouldAttemptReset() {
                state = .halfOpen
            } else {
                throw CategorizationError.rateLimitExceeded
            }
        case .halfOpen:
            break
        case .closed:
            break
        }
        
        do {
            let result = try await operation()
            onSuccess()
            return result
        } catch {
            onFailure()
            throw error
        }
    }
    
    private func onSuccess() {
        failureCount = 0
        state = .closed
    }
    
    private func onFailure() {
        failureCount += 1
        lastFailureTime = Date()
        
        if failureCount >= failureThreshold {
            state = .open
        }
    }
    
    private func shouldAttemptReset() -> Bool {
        guard let lastFailure = lastFailureTime else { return true }
        return Date().timeIntervalSince(lastFailure) >= recoveryTimeout
    }
}

// MARK: - Logger Protocol and Implementation

protocol Logger {
    func debug(_ message: String)
    func info(_ message: String)
    func warning(_ message: String)
    func error(_ message: String)
}

class ConsoleLogger: Logger {
    func debug(_ message: String) {
        print("🔍 DEBUG: \(message)")
    }
    
    func info(_ message: String) {
        print("ℹ️ INFO: \(message)")
    }
    
    func warning(_ message: String) {
        print("⚠️ WARNING: \(message)")
    }
    
    func error(_ message: String) {
        print("❌ ERROR: \(message)")
    }
}

// MARK: - Result Type for Better Error Handling

enum Result<Success, Failure: Error> {
    case success(Success)
    case failure(Failure)
    
    func map<NewSuccess>(_ transform: (Success) -> NewSuccess) -> Result<NewSuccess, Failure> {
        switch self {
        case .success(let value):
            return .success(transform(value))
        case .failure(let error):
            return .failure(error)
        }
    }
    
    func flatMap<NewSuccess>(_ transform: (Success) -> Result<NewSuccess, Failure>) -> Result<NewSuccess, Failure> {
        switch self {
        case .success(let value):
            return transform(value)
        case .failure(let error):
            return .failure(error)
        }
    }
    
    func get() throws -> Success {
        switch self {
        case .success(let value):
            return value
        case .failure(let error):
            throw error
        }
    }
}

// MARK: - Validation Extensions

extension Validator where T == String {
    static var urlValidator: AnyValidator<String> {
        AnyValidator { url in
            guard !url.isEmpty else {
                return .invalid(errors: ["URL cannot be empty"])
            }
            
            guard URL(string: url) != nil else {
                return .invalid(errors: ["Invalid URL format"])
            }
            
            return .valid
        }
    }
    
    static var categoryNameValidator: AnyValidator<String> {
        AnyValidator { name in
            var errors: [String] = []
            var warnings: [String] = []
            
            if name.isEmpty {
                errors.append("Category name cannot be empty")
            }
            
            if name.count > 50 {
                warnings.append("Category name is quite long")
            }
            
            if name.contains(CharacterSet.controlCharacters) {
                errors.append("Category name contains invalid characters")
            }
            
            return ValidationResult(isValid: errors.isEmpty, errors: errors, warnings: warnings)
        }
    }
}

struct AnyValidator<T>: Validator {
    private let _validate: (T) -> ValidationResult
    
    init(_ validate: @escaping (T) -> ValidationResult) {
        self._validate = validate
    }
    
    func validate(_ item: T) -> ValidationResult {
        return _validate(item)
    }
}
