//
//  ContentView.swift
//  Pebl
//
//  Created by <PERSON><PERSON> on 6/4/25.
//

import SwiftUI


struct ContentView: View {
    @State private var inputMessage: String = ""
    @StateObject private var categoryManager = CategoryManager()
    @State private var selectedCategory: Category? = nil

    var body: some View {
        NavigationView {
            VStack {
                if let selectedCategory = selectedCategory {
                    CategoryDetailView(
                        category: selectedCategory,
                        categoryManager: categoryManager,
                        onBack: { self.selectedCategory = nil },
                        onNavigateToSubcategory: { subcategory in
                            self.selectedCategory = subcategory
                        }
                    )
                } else {
                    // Main screen
                    VStack(spacing: 5) {
                        TextField("Enter your message here...", text: $inputMessage)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .padding()
                        
                        Button("Store") {
                            categorizeMessage(inputMessage)
                            inputMessage = ""
                        }
                        .font(.headline)
                        .padding()
                        .frame(width: 100, height: 50)
                        .background(Color.white)
                        .foregroundColor(.black)
                        .cornerRadius(10)
                        .shadow(color: Color.gray.opacity(0.5), radius: 5, x: 0, y: 5)
                        .padding(.bottom, 50)

                        ScrollView {
                            LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())]) {
                                ForEach(categoryManager.rootCategories.sorted(by: { $0.name < $1.name }), id: \.id) { category in
                                    Button(action: {
                                        self.selectedCategory = category
                                    }) {
                                        VStack {
                                            Image(systemName: category.sfSymbol)
                                                .resizable()
                                                .scaledToFit()
                                                .frame(width: 120, height: 40)
                                                .foregroundColor(.black)
                                            HStack {
                                                Text(category.name)
                                                    .foregroundColor(.black)
                                                Text("(\(category.getTotalMessageCount()))")
                                                    .font(.caption)
                                                    .foregroundColor(.gray)
                                            }
                                        }
                                        .frame(width: 180, height: 120)
                                        .background(Color.white)
                                        .cornerRadius(10)
                                        .shadow(color: Color.gray.opacity(0.5), radius: 5, x: 0, y: 5)
                                    }
                                }
                            }
                            .padding(.bottom, 20)

                            Button("Add Category") {
                                let alert = UIAlertController(title: "New Category", message: "Enter a name for the new category", preferredStyle: .alert)
                                alert.addTextField { textField in
                                    textField.placeholder = "Category name"
                                }
                                alert.addAction(UIAlertAction(title: "Add", style: .default) { _ in
                                    if let categoryName = alert.textFields?.first?.text, !categoryName.isEmpty {
                                        DispatchQueue.main.async {
                                            addNewCategory(categoryName)
                                        }
                                    }
                                })
                                alert.addAction(UIAlertAction(title: "Cancel", style: .cancel, handler: nil))

                                if let scene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                                   let window = scene.windows.first,
                                   let rootVC = window.rootViewController {
                                    rootVC.present(alert, animated: true, completion: nil)
                                }
                            }
                            .font(.headline)
                            .padding()
                            .frame(width: 150, height: 50)
                            .background(Color.white)
                            .foregroundColor(.black)
                            .cornerRadius(10)
                            .shadow(color: Color.gray.opacity(0.5), radius: 5, x: 0, y: 5)
                            .padding(.bottom, 40)
                        }
                    }
                    .navigationTitle("Pebl")
                }
            }
            .onAppear {
                DispatchQueue.main.async {
                    loadCategoriesFromFile()
                }
            }
        }
    }

    private func categorizeMessage(_ message: String) {
        guard !message.isEmpty else { return }

        let aiModel = AIModel()
        inputMessage = "" // Clear input immediately for better UX

        categoryManager.categorizeAndAddMessage(message, using: aiModel) {
            // This completion block runs on main thread after categorization
            self.saveCategoriesToFile()
        }
    }

    private func addNewCategory(_ newCategory: String) {
        guard !newCategory.isEmpty else { return }

        // Create category with default icon first for immediate UI feedback
        let category = categoryManager.addRootCategory(name: newCategory, sfSymbol: "folder.fill")
        saveCategoriesToFile()

        // Get appropriate SF symbol in background
        DispatchQueue.global(qos: .userInitiated).async {
            let aiModel = AIModel()
            let sfSymbol = aiModel.getSFSymbolForCategory(newCategory)

            DispatchQueue.main.async {
                category.sfSymbol = sfSymbol
                self.saveCategoriesToFile()
            }
        }
    }

    // This function is now handled in CategoryDetailView

    private func saveCategoriesToFile() {
        let fileManager = FileManager.default
        if let documentDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first {
            let fileURL = documentDirectory.appendingPathComponent("categories.json")
            do {
                let data = try JSONEncoder().encode(categoryManager.rootCategories)
                try data.write(to: fileURL)
            } catch {
                print("Error saving categories: \(error)")
            }
        }
    }

    private func loadCategoriesFromFile() {
        let fileManager = FileManager.default
        if let documentDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first {
            let fileURL = documentDirectory.appendingPathComponent("categories.json")
            if fileManager.fileExists(atPath: fileURL.path) {
                do {
                    let data = try Data(contentsOf: fileURL)

                    // Try to decode as new format first
                    if let loadedCategories = try? JSONDecoder().decode([Category].self, from: data) {
                        categoryManager.rootCategories = loadedCategories
                        // Set categoryManager reference for all loaded categories
                        setCategoryManagerReferences(for: loadedCategories)
                        return
                    }

                    // If that fails, try to decode as old format and migrate
                    if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                       let oldCategories = json["categories"] as? [String: [String]],
                       let oldCategoryImages = json["categoryImages"] as? [String: String] {

                        // Migrate old format to new format
                        categoryManager.rootCategories = []
                        for (categoryName, messageStrings) in oldCategories {
                            let sfSymbol = oldCategoryImages[categoryName] ?? "folder.fill"
                            let category = categoryManager.addRootCategory(name: categoryName, sfSymbol: sfSymbol)
                            for messageString in messageStrings {
                                category.addMessage(messageString)
                            }
                        }

                        // Save in new format
                        saveCategoriesToFile()
                        print("Successfully migrated categories from old format to new format")
                        return
                    }

                    print("Could not decode categories file in any known format")

                } catch {
                    print("Error loading categories: \(error)")
                    // Keep default categories if loading fails
                }
            }
            // Default categories are already loaded in CategoryManager init
        }
    }

    /// Set categoryManager references for loaded categories (recursive)
    private func setCategoryManagerReferences(for categories: [Category]) {
        for category in categories {
            category.categoryManager = categoryManager
            setCategoryManagerReferences(for: category.subcategories)
        }
    }
}

// MARK: - Category Detail View

struct CategoryDetailView: View {
    @ObservedObject var category: Category
    @ObservedObject var categoryManager: CategoryManager
    let onBack: () -> Void
    let onNavigateToSubcategory: (Category) -> Void

    var body: some View {
        VStack {
            // Messages in this category
            if !category.messages.isEmpty {
                List {
                    ForEach(category.messages, id: \.id) { message in
                        HStack {
                            Button(action: {
                                category.toggleMessageCompletion(withId: message.id)
                            }) {
                                Image(systemName: message.isCompleted ? "checkmark.circle.fill" : "circle")
                                    .foregroundColor(message.isCompleted ? .green : .gray)
                            }
                            .buttonStyle(PlainButtonStyle())

                            Text(message.text)
                                .strikethrough(message.isCompleted)
                                .foregroundColor(message.isCompleted ? .gray : .primary)

                            Spacer()
                        }
                        .swipeActions(edge: .trailing) {
                            Button("Delete") {
                                category.removeMessage(withId: message.id)
                            }
                            .tint(.red)
                        }
                    }
                }

                // Clear completed messages button
                if category.messages.contains(where: { $0.isCompleted }) {
                    Button("Clear Completed") {
                        category.removeCompletedMessages()
                    }
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.orange.opacity(0.1))
                    .foregroundColor(.orange)
                    .cornerRadius(5)
                    .padding(.bottom, 10)
                }
            }

            // Subcategories
            if !category.subcategories.isEmpty {
                Text("Subcategories")
                    .font(.headline)
                    .padding(.top)

                LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())]) {
                    ForEach(category.subcategories.sorted(by: { $0.name < $1.name }), id: \.id) { subcategory in
                        Button(action: {
                            onNavigateToSubcategory(subcategory)
                        }) {
                            VStack {
                                Image(systemName: subcategory.sfSymbol)
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: 60, height: 20)
                                    .foregroundColor(.black)
                                HStack {
                                    Text(subcategory.name)
                                        .foregroundColor(.black)
                                        .font(.caption)
                                    Text("\(subcategory.getTotalMessageCount())")
                                        .font(.caption2)
                                        .foregroundColor(.gray)
                                }
                            }
                            .frame(width: 120, height: 80)
                            .background(Color.white)
                            .cornerRadius(8)
                            .shadow(color: Color.gray.opacity(0.3), radius: 3, x: 0, y: 3)
                        }
                    }
                }
                .padding(.bottom, 20)
            }

            // Action buttons
            HStack {
                Button("Add Subcategory") {
                    showAddSubcategoryAlert()
                }
                .font(.caption)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color.blue.opacity(0.1))
                .foregroundColor(.blue)
                .cornerRadius(5)

                Button("Delete Category") {
                    showDeleteCategoryAlert()
                }
                .font(.caption)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color.red.opacity(0.1))
                .foregroundColor(.red)
                .cornerRadius(5)
            }
            .padding(.bottom, 40)
        }
        .navigationTitle(category.name)
        .toolbar {
            Button("Back") {
                onBack()
            }
            .foregroundColor(.black)
        }
    }

    private func showAddSubcategoryAlert() {
        let alert = UIAlertController(title: "New Subcategory", message: "Enter a name for the new subcategory", preferredStyle: .alert)
        alert.addTextField { textField in
            textField.placeholder = "Subcategory name"
        }
        alert.addAction(UIAlertAction(title: "Add", style: .default) { _ in
            if let subcategoryName = alert.textFields?.first?.text, !subcategoryName.isEmpty {
                // Create subcategory with default icon first for immediate UI feedback
                let newSubcategory = category.addSubcategory(name: subcategoryName, sfSymbol: "folder.fill")

                // Get appropriate SF symbol in background
                DispatchQueue.global(qos: .userInitiated).async {
                    let aiModel = AIModel()
                    let sfSymbol = aiModel.getSFSymbolForCategory(subcategoryName)

                    DispatchQueue.main.async {
                        newSubcategory.sfSymbol = sfSymbol
                    }
                }
            }
        })
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel, handler: nil))

        if let scene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = scene.windows.first,
           let rootVC = window.rootViewController {
            rootVC.present(alert, animated: true, completion: nil)
        }
    }

    private func showDeleteCategoryAlert() {
        let alert = UIAlertController(title: "Delete Category", message: "Do you want to distribute the contents to other categories?", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "Yes", style: .default) { _ in
            DispatchQueue.main.async {
                let aiModel = AIModel()
                category.redistributeMessages(using: aiModel, to: categoryManager.rootCategories)

                // Remove this category from its parent or root
                if let parent = category.parent {
                    parent.removeSubcategory(category)
                } else {
                    categoryManager.removeRootCategory(category)
                }
                onBack()
            }
        })
        alert.addAction(UIAlertAction(title: "No, delete all", style: .destructive) { _ in
            if let parent = category.parent {
                parent.removeSubcategory(category)
            } else {
                categoryManager.removeRootCategory(category)
            }
            onBack()
        })
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel, handler: nil))

        if let scene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = scene.windows.first,
           let rootVC = window.rootViewController {
            rootVC.present(alert, animated: true, completion: nil)
        }
    }
}

#Preview {
    ContentView()
}
