//
//  AIModelTests.swift
//  Pebl
//
//  Created by AI Assistant on 6/17/25.
//

import Foundation

/// Test class to demonstrate the enhanced AI categorization capabilities
class AIModelTests {
    
    static func runTests() {
        print("🧪 Testing Enhanced AI Categorization System")
        print("=" * 50)
        
        let aiModel = AIModel()
        let categoryManager = CategoryManager()
        
        // Test cases that demonstrate the improvements
        let testMessages = [
            // Movie examples with genre detection
            "Watch The Dark Knight - Batman movie",
            "Inception - Christopher Nolan sci-fi thriller",
            "The Hangover - comedy movie",
            "Titanic - romantic drama",
            "Get Out - horror thriller",
            "Free Solo - climbing documentary",

            // Shopping examples with category detection
            "Buy iPhone 15 Pro",
            "Order Nike running shoes",
            "Purchase The Great Gatsby book",
            "Get organic vegetables from Whole Foods",
            "Buy IKEA desk for home office",

            // URL examples
            "https://www.amazon.com/dp/B08N5WRWNW - AirPods Pro",
            "https://www.netflix.com/title/80057281 - Stranger Things",
            "https://stackoverflow.com/questions/12345 - Swift programming help",
            "https://www.nytimes.com/2023/12/01/technology/ai-news.html",
            "https://docs.swift.org/swift-book/ - Swift documentation",

            // Task examples with priority/type detection
            "URGENT: Submit quarterly report by Friday",
            "Schedule dentist appointment",
            "Plan weekend trip to San Francisco",
            "Review code for iOS app project",
            "Buy groceries for dinner party",

            // Reading examples with type detection
            "Read 'Atomic Habits' by James Clear",
            "Check out this AI research paper on neural networks",
            "Read TechCrunch article about startup funding",
            "Study Swift programming tutorial",

            // NEW: Creative life project examples
            "Research character development techniques for my novel",
            "Outline plot structure for fantasy book",
            "Find beta readers for manuscript",
            "Look into self-publishing options",

            "Schedule house viewing for Saturday",
            "Get pre-approved for mortgage",
            "Research neighborhood schools",
            "Contact real estate lawyer",

            "Buy prenatal vitamins",
            "Research pediatricians in area",
            "Set up baby registry",
            "Plan nursery layout",

            "Book engagement photographer",
            "Research wedding venues",
            "Create guest list spreadsheet",
            "Schedule cake tasting",

            "Update resume for career change",
            "Network with people in tech industry",
            "Take online course in data science",
            "Practice coding interview questions",
        ]
        
        print("\n📝 Testing Message Categorization:")
        print("-" * 30)
        
        for (index, message) in testMessages.enumerated() {
            print("\n\(index + 1). Message: \"\(message)\"")
            
            // Test the enhanced categorization
            let result = aiModel.categorizeMessageWithContext(message, categoryManager: categoryManager)
            
            if let category = result.category {
                print("   ✅ Category: \(category.name)")
                if result.shouldCreateSubfolder, let subfolderName = result.subfolderName {
                    print("   📁 Suggested Subfolder: \(subfolderName)")
                }
            } else {
                // Would create new category
                let availableCategories = categoryManager.getAllCategoryNames()
                let categoryName = aiModel.categorizeMessage(message, availableCategories: availableCategories)
                print("   🆕 New Category: \(categoryName)")
            }
            
            // Test URL analysis if message contains URLs
            let urls = aiModel.extractURLsFromMessage(message)
            if !urls.isEmpty {
                for url in urls {
                    let urlContext = aiModel.analyzeURLContent(url)
                    print("   🔗 URL Context: \(urlContext)")
                }
            }
        }
        
        print("\n\n🎯 Key Improvements Demonstrated:")
        print("-" * 40)
        print("✅ Enhanced prompts with better context understanding")
        print("✅ URL domain analysis for better categorization")
        print("✅ Smart subfolder creation based on content patterns")
        print("✅ Context-aware categorization considering existing structure")
        print("✅ Preference for existing categories over creating new ones")
        print("✅ Genre-based movie categorization")
        print("✅ Type-based shopping categorization")
        print("✅ Priority-based task categorization")
        
        print("\n\n📋 Subfolder Creation Rules:")
        print("-" * 35)
        print("• Movies: Comedy, Action & Adventure, Drama & Romance, Horror & Thriller, Documentaries")
        print("• Shopping: Clothing & Fashion, Electronics, Books, Food & Grocery, Home & Garden")
        print("• To-Do: Urgent, Work, Personal")
        print("• To-Read: Articles & Blogs, Books, Research & Papers")
        print("• Minimum 5 messages in category before creating subfolders")
        
        print("\n\n🌐 URL Analysis Categories:")
        print("-" * 30)
        print("• Shopping: Amazon, eBay, Etsy, Walmart, Target, etc.")
        print("• Entertainment: Netflix, YouTube, IMDB, Spotify, etc.")
        print("• News: CNN, BBC, NYTimes, WSJ, Guardian, etc.")
        print("• Learning: Docs, Tutorials, Stack Overflow, GitHub, etc.")
        print("• Work: Slack, Teams, Zoom, Office, Google Drive, etc.")
        print("• Health: Medical sites, fitness, nutrition, etc.")
        print("• Travel: Booking, Airbnb, Expedia, etc.")

        // Test category optimization
        print("\n\n🔍 Testing Category Optimization:")
        print("-" * 40)
        testCategoryOptimization(aiModel: aiModel, categoryManager: categoryManager)
    }

    static func testCategoryOptimization(aiModel: AIModel, categoryManager: CategoryManager) {
        // Create a test category with multiple messages to analyze
        let bookWritingCategory = categoryManager.addRootCategory(name: "Book Writing", sfSymbol: "book.closed")

        // Add sample messages that should suggest natural subfolders
        let bookMessages = [
            "Research medieval history for fantasy setting",
            "Develop main character backstory",
            "Outline three-act structure",
            "Write first chapter draft",
            "Research publishing options",
            "Create character relationship map",
            "Plot timeline for story events",
            "Edit chapter 2 for pacing",
            "Find beta readers for feedback",
            "Research book cover design"
        ]

        for message in bookMessages {
            bookWritingCategory.addMessage(message)
        }

        print("📚 Analyzing 'Book Writing' category with \(bookMessages.count) messages...")
        let suggestions = aiModel.analyzeCategoryForOptimization(bookWritingCategory)

        if !suggestions.isEmpty {
            print("   💡 Suggested subfolders:")
            for suggestion in suggestions {
                print("      • \(suggestion)")
            }
        } else {
            print("   ℹ️ No subfolder suggestions (category may need more diverse content)")
        }

        // Test dynamic subfolder creation
        print("\n🎯 Testing Dynamic Subfolder Creation:")
        let newMessage = "Create detailed character profiles for antagonist"
        let subfolderResult = aiModel.shouldCreateSubfolder(for: newMessage, in: bookWritingCategory)

        if subfolderResult.shouldCreate, let subfolderName = subfolderResult.subfolderName {
            print("   📁 Would create subfolder: '\(subfolderName)' for message: '\(newMessage)'")
        } else {
            print("   ➡️ Would add directly to main category: '\(newMessage)'")
        }
    }
}

// Extension to make string multiplication work for formatting
extension String {
    static func * (left: String, right: Int) -> String {
        return String(repeating: left, count: right)
    }
}
