//
//  AIService.swift
//  Pebl
//
//  Created by AI Assistant on 6/17/25.
//

import Foundation

// MARK: - OpenAI Service Implementation

class OpenAIService: AIServiceProtocol {
    private let config: ConfigurationProvider
    private let logger: Logger
    private let session: URLSession
    
    init(config: ConfigurationProvider, logger: Logger = ConsoleLogger()) {
        self.config = config
        self.logger = logger
        
        // Configure URLSession with timeout
        let configuration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = config.getAISettings().timeouts.aiRequest
        self.session = URLSession(configuration: configuration)
    }
    
    func makeRequest(prompt: String, maxTokens: Int, timeout: TimeInterval) async throws -> String {
        guard let apiKey = ProcessInfo.processInfo.environment["OPENAI_API_KEY"] else {
            throw CategorizationError.apiKeyMissing
        }
        
        guard let url = URL(string: "https://api.openai.com/v1/chat/completions") else {
            throw CategorizationError.invalidURL("OpenAI API URL")
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.timeoutInterval = timeout
        
        let aiSettings = config.getAISettings()
        let parameters: [String: Any] = [
            "model": aiSettings.model,
            "messages": [
                ["role": "system", "content": "You are an intelligent categorization assistant for a personal organization app."],
                ["role": "user", "content": prompt]
            ],
            "max_tokens": maxTokens
        ]
        
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: parameters, options: [])
            request.httpBody = jsonData
        } catch {
            throw CategorizationError.invalidConfiguration("Failed to serialize request: \(error)")
        }
        
        logger.debug("Making AI request with prompt length: \(prompt.count)")
        
        do {
            let (data, response) = try await session.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                throw CategorizationError.invalidResponse
            }
            
            switch httpResponse.statusCode {
            case 200:
                return try parseResponse(data)
            case 429:
                throw CategorizationError.rateLimitExceeded
            case 401:
                throw CategorizationError.apiKeyMissing
            default:
                throw CategorizationError.invalidResponse
            }
        } catch {
            if error is CategorizationError {
                throw error
            } else {
                throw CategorizationError.networkError(error)
            }
        }
    }
    
    private func parseResponse(_ data: Data) throws -> String {
        do {
            guard let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
                  let choices = json["choices"] as? [[String: Any]],
                  let message = choices.first?["message"] as? [String: Any],
                  let content = message["content"] as? String else {
                throw CategorizationError.invalidResponse
            }
            
            return content.trimmingCharacters(in: .whitespacesAndNewlines)
        } catch {
            throw CategorizationError.invalidResponse
        }
    }
}

// MARK: - Message Categorizer Implementation

class AIMessageCategorizer: MessageCategorizer {
    private let aiService: AIServiceProtocol
    private let urlAnalyzer: URLAnalyzer
    private let cache: AnalysisCache
    private let config: ConfigurationProvider
    private let logger: Logger
    
    init(aiService: AIServiceProtocol,
         urlAnalyzer: URLAnalyzer,
         cache: AnalysisCache,
         config: ConfigurationProvider,
         logger: Logger = ConsoleLogger()) {
        self.aiService = aiService
        self.urlAnalyzer = urlAnalyzer
        self.cache = cache
        self.config = config
        self.logger = logger
    }
    
    func categorizeMessage(_ message: String, availableCategories: [String]) async throws -> String {
        guard !availableCategories.isEmpty else {
            throw CategorizationError.invalidConfiguration("No available categories provided")
        }
        
        // Check cache first
        let cacheKey = CacheKeyGenerator.categorizationKey(for: message, categories: availableCategories)
        if let cachedResult = cache.getCachedResult(for: cacheKey, type: String.self) {
            logger.debug("Using cached categorization result")
            return cachedResult
        }
        
        // Analyze URLs in the message for context
        let urlContext = try await analyzeURLsInMessage(message)
        
        // Perform AI categorization
        let result = try await performCategorization(
            message: message,
            availableCategories: availableCategories,
            urlContext: urlContext
        )
        
        // Cache the result
        let cachingConfig = config.getCachingConfig()
        if cachingConfig.enabled {
            cache.setCachedResult(result, for: cacheKey, ttl: cachingConfig.categoryAnalysisTTL)
        }
        
        return result
    }
    
    func categorizeMessageWithContext(_ message: String, categoryManager: CategoryManagerProtocol) async throws -> CategorizationResult {
        let availableCategories = categoryManager.getAllCategoryNames()
        let categoryName = try await categorizeMessage(message, availableCategories: availableCategories)
        
        // Find the target category
        guard let targetCategory = categoryManager.findCategory(named: categoryName) else {
            // Category doesn't exist, will be created as root category
            return CategorizationResult(
                category: nil,
                shouldCreateSubfolder: false,
                subfolderName: nil,
                confidence: 0.8
            )
        }
        
        // Check if we should create a subfolder using the subfolder analyzer
        let subfolderAnalyzer = ServiceLocator.shared.getSubfolderAnalyzer()
        let subfolderSuggestion = try await subfolderAnalyzer.shouldCreateSubfolder(for: message, in: targetCategory)
        
        if subfolderSuggestion.shouldCreate, let subfolderName = subfolderSuggestion.subfolderName {
            // Check if subfolder already exists
            if let existingSubfolder = targetCategory.subcategories.first(where: { $0.name == subfolderName }) {
                return CategorizationResult(
                    category: existingSubfolder,
                    shouldCreateSubfolder: false,
                    subfolderName: nil,
                    confidence: subfolderSuggestion.confidence
                )
            } else {
                return CategorizationResult(
                    category: targetCategory,
                    shouldCreateSubfolder: true,
                    subfolderName: subfolderName,
                    confidence: subfolderSuggestion.confidence
                )
            }
        }
        
        return CategorizationResult(
            category: targetCategory,
            shouldCreateSubfolder: false,
            subfolderName: nil,
            confidence: 0.8
        )
    }
    
    private func analyzeURLsInMessage(_ message: String) async -> String {
        let urls = extractURLsFromMessage(message)
        
        if urls.isEmpty {
            return ""
        }
        
        do {
            let urlAnalysis = try await urlAnalyzer.analyzeURL(urls[0])
            return urlAnalysis.category
        } catch {
            logger.warning("Failed to analyze URL: \(error)")
            return ""
        }
    }
    
    private func extractURLsFromMessage(_ message: String) -> [String] {
        guard let detector = try? NSDataDetector(types: NSTextCheckingResult.CheckingType.link.rawValue) else {
            return []
        }
        
        let matches = detector.matches(in: message, options: [], range: NSRange(location: 0, length: message.utf16.count))
        
        return matches.compactMap { match in
            if let range = Range(match.range, in: message) {
                return String(message[range])
            }
            return nil
        }
    }
    
    private func performCategorization(message: String, availableCategories: [String], urlContext: String) async throws -> String {
        let aiSettings = config.getAISettings()
        let urlAnalysis = urlContext.isEmpty ? "" : "\n\nURL Analysis: The message contains URLs that appear to be related to: \(urlContext)"
        
        let prompt = """
        Categorize the following message into one of these existing categories: \(availableCategories.joined(separator: ", ")).
        
        IMPORTANT RULES:
        1. STRONGLY prefer existing categories over creating new ones (90% of the time use existing)
        2. For movies/shows: Consider if they fit into existing entertainment categories
        3. For shopping items: Use "Shopping" unless there's a more specific existing category
        4. For URLs: Consider the website content and purpose, not just the URL itself
        5. For tasks/reminders: Use "To-Do" unless there's a more specific category
        6. For books/articles: Use "To-Read" unless there's a more specific category
        7. Look for semantic similarity - "Book Writing" could fit in "Writing", "House Hunting" could fit in "Real Estate" or "Home"
        8. Consider broader themes - baby planning could fit in "Family", travel planning in "Travel", etc.
        
        Only create a NEW category if:
        - The message represents a major life project/area (like "Wedding Planning", "Career Change", "Health Journey")
        - It's a significant ongoing endeavor that will generate many related messages
        - It truly doesn't fit semantically into any existing category
        
        If you must create a new category, make it:
        - Broad enough for future similar items (think "Book Writing" not "Chapter 3 Notes")
        - Clear and descriptive of the overall project/area
        - Properly capitalized (e.g., "House Buying", "Baby Planning", "Business Startup")
        - Future-oriented (will this category be useful for 10+ related messages?)
        
        Examples of good new categories for major life projects:
        - "Wedding Planning" (for engagement, venue, catering, etc.)
        - "House Buying" (for research, viewing, financing, moving)
        - "Baby Planning" (for preparation, medical, shopping, setup)
        - "Book Writing" (for research, writing, editing, publishing)
        - "Career Change" (for job search, networking, skill building)
        - "Health Journey" (for fitness, nutrition, medical, wellness)
        - "Business Startup" (for planning, legal, marketing, development)
        
        Return ONLY the category name, nothing else.\(urlAnalysis)
        
        Message: \(message)
        """
        
        return try await aiService.makeRequest(
            prompt: prompt,
            maxTokens: aiSettings.maxTokens.categorization,
            timeout: aiSettings.timeouts.aiRequest
        )
    }
}

// MARK: - Symbol Generator Implementation

class AISymbolGenerator: SymbolGenerator {
    private let aiService: AIServiceProtocol
    private let cache: AnalysisCache
    private let config: ConfigurationProvider
    private let logger: Logger
    
    init(aiService: AIServiceProtocol,
         cache: AnalysisCache,
         config: ConfigurationProvider,
         logger: Logger = ConsoleLogger()) {
        self.aiService = aiService
        self.cache = cache
        self.config = config
        self.logger = logger
    }
    
    func getSFSymbol(for categoryName: String) async throws -> String {
        // Check cache first
        let cacheKey = CacheKeyGenerator.sfSymbolKey(for: categoryName)
        if let cachedResult = cache.getCachedResult(for: cacheKey, type: String.self) {
            logger.debug("Using cached SF symbol for category: \(categoryName)")
            return cachedResult
        }
        
        let aiSettings = config.getAISettings()
        let prompt = "What is the most apt SF Symbol that can be used in SwiftUI App to represent the category \"\(categoryName)\"? Only return the SF Symbol string that can be used in SwiftUI App as the value for systemName. No other formatting and nothing else."
        
        let result = try await aiService.makeRequest(
            prompt: prompt,
            maxTokens: aiSettings.maxTokens.sfSymbol,
            timeout: aiSettings.timeouts.aiRequest
        )
        
        let cleanedResult = result.trimmingCharacters(in: CharacterSet.whitespacesAndNewlines.union(CharacterSet(charactersIn: "\"")))
        
        // Cache the result
        let cachingConfig = config.getCachingConfig()
        if cachingConfig.enabled {
            cache.setCachedResult(cleanedResult, for: cacheKey, ttl: cachingConfig.categoryAnalysisTTL)
        }
        
        return cleanedResult
    }
}
